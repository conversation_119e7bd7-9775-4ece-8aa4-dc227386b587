const { Slash<PERSON>ommandBuilder, EmbedBuilder, MessageFlags } = require('discord.js');
const { useQueue, usePlayer } = require('discord-player');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('nowplaying')
        .setDescription('Shows the currently playing song'),
    
    async execute(interaction) {
        const queue = useQueue(interaction.guild.id);
        const player = usePlayer(interaction.guild.id);

        if (!queue || !queue.currentTrack) {
            return interaction.reply({ 
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ There is no song playing!')
                        .setColor('#FF0000')
                ],
                ephemeral: true
            });
        }

        const track = queue.currentTrack;
        const requestedBy = track.requestedBy?.username || 'Unknown';
        const artist = track.author || 'Unknown'; // Get artist name
        const trackPosition = player.getTrackPosition() + 1; // Correct track index
        const timestamp = player.getTimestamp();
        const progressBar = player.createProgressBar();

        const embed = new EmbedBuilder()
            .setTitle('🎶 Now Playing')
            .setDescription(`**[${track.title}](${track.url})**`)
            .setThumbnail(track.thumbnail)
            .addFields(
                { name: '🎤 Artist', value: artist, inline: true },
                { name: '📌 Track Position', value: `${trackPosition} / ${queue.tracks.size + 1}`, inline: true },
                { name: '🕒 Duration', value: track.duration, inline: true },
                { name: '🙍 Requested by', value: requestedBy, inline: true },
                { name: '🎵 Status', value: queue.node.isPaused() ? '⏸️ Paused' : '▶️ Playing', inline: true },
                { name: '🔁 Loop Mode', value: queue.repeatMode ? '🔁 Enabled' : '❌ Off', inline: true },
                { name: '🔊 Progress', value: `${progressBar}\n\`${timestamp.progress}%\``, inline: false }
            )
            .setColor('#00FF00')
            .setFooter({ text: 'Powered by Discord Player', iconURL: interaction.client.user.displayAvatarURL() }) // Nice footer
            .setTimestamp();

        return interaction.reply({ embeds: [embed] });
    }
};
