const { EmbedBuilder } = require("discord.js");
const { gameDB } = require("../../database/manager")
module.exports = {
    name: "minesweeper",
    aliases: ["ms"],
    category: "game",
    description: "Play Minesweeper!",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const subcommand = args[0];

        if (subcommand === "start") {
            const size = parseInt(args[1]) || 5;
            const mines = parseInt(args[2]) || Math.floor(size * size * 0.2);

            if (size < 3 || size > 10) return message.reply("⚠ Grid size must be between **3 and 10**.");
            if (mines < 1 || mines >= size * size) return message.reply("⚠ Invalid mine count.");

            const board = generateBoard(size, mines);
            const embed = createBoardEmbed(board, [], [], size, message.author);

            const gameMessage = await message.channel.send({ embeds: [embed] });

            await gameDB.set(`ms_${message.author.id}`, {
                size, board, revealed: [], flagged: [],
                messageId: gameMessage.id
            });

            return;
        }

        if (subcommand === "reveal" || subcommand === "flag") {
            const x = parseInt(args[1]), y = parseInt(args[2]);
            if (isNaN(x) || isNaN(y)) {
                return message.reply(`⚠ Usage: \`!ms ${subcommand} x y\``).then(msg => setTimeout(() => msg.delete(), 3000));
            }

            const game = await gameDB.get(`ms_${message.author.id}`);
            if (!game) return message.reply("⚠ Start a game first with `!ms start`!");

            const { board, revealed, flagged, size, messageId } = game;
            const gameMessage = await message.channel.messages.fetch(messageId).catch(() => null);
            if (!gameMessage) return message.reply("⚠ Could not find the game message. Start a new game!");

            if (subcommand === "reveal") {
                if (revealed.some(([rx, ry]) => rx === x && ry === y)) return message.reply("⚠ Tile already revealed!").then(msg => setTimeout(() => msg.delete(), 3000));
                if (flagged.some(([fx, fy]) => fx === x && fy === y)) return message.reply("⚠ Tile is flagged!").then(msg => setTimeout(() => msg.delete(), 3000));

                if (board[y][x] === "💣") {
                    await gameDB.delete(`ms_${message.author.id}`);
                    const gameOverEmbed = createGameOverEmbed(board, message.author);
                    return gameMessage.edit({ embeds: [gameOverEmbed] });
                }

                const newRevealed = floodFill(x, y, board, revealed);
                await gameDB.set(`ms_${message.author.id}.revealed`, newRevealed);

                const updatedEmbed = createBoardEmbed(board, newRevealed, flagged, size, message.author);
                await gameMessage.edit({ embeds: [updatedEmbed] });
            }

            if (subcommand === "flag") {
                if (flagged.some(([fx, fy]) => fx === x && fy === y)) {
                    return message.reply("⚠ Tile already flagged!").then(msg => setTimeout(() => msg.delete(), 3000));
                }

                flagged.push([x, y]);
                await gameDB.set(`ms_${message.author.id}.flagged`, flagged);

                const updatedEmbed = createBoardEmbed(board, revealed, flagged, size, message.author);
                await gameMessage.edit({ embeds: [updatedEmbed] });
            }

            await message.delete();
        }
    }
};

function generateBoard(size, mineCount) {
    let board = Array.from({ length: size }, () => Array(size).fill(0));
    let minePositions = new Set();

    while (minePositions.size < mineCount) {
        let x = Math.floor(Math.random() * size);
        let y = Math.floor(Math.random() * size);
        minePositions.add(`${x},${y}`);
    }

    minePositions.forEach(pos => {
        let [x, y] = pos.split(",").map(Number);
        board[y][x] = "💣";

        for (let dx = -1; dx <= 1; dx++) {
            for (let dy = -1; dy <= 1; dy++) {
                let nx = x + dx, ny = y + dy;
                if (nx >= 0 && nx < size && ny >= 0 && ny < size && board[ny][nx] !== "💣") {
                    board[ny][nx]++;
                }
            }
        }
    });

    return board;
}

function floodFill(x, y, board, revealed) {
    const queue = [[x, y]];
    const visited = new Set(revealed.map(([rx, ry]) => `${rx},${ry}`));

    while (queue.length) {
        const [cx, cy] = queue.shift();
        if (visited.has(`${cx},${cy}`)) continue;

        revealed.push([cx, cy]);
        visited.add(`${cx},${cy}`);

        if (board[cy][cx] === 0) {
            for (let dx = -1; dx <= 1; dx++) {
                for (let dy = -1; dy <= 1; dy++) {
                    let nx = cx + dx, ny = cy + dy;
                    if (nx >= 0 && ny >= 0 && nx < board.length && ny < board.length) {
                        queue.push([nx, ny]);
                    }
                }
            }
        }
    }

    return revealed;
}

function createBoardEmbed(board, revealed, flagged, size, user) {
    let display = "";
    for (let y = 0; y < size; y++) {
        for (let x = 0; x < size; x++) {
            if (flagged.some(([fx, fy]) => fx === x && fy === y)) {
                display += "🚩 ";
            } else if (revealed.some(([rx, ry]) => rx === x && ry === y)) {
                display += board[y][x] === 0 ? "⬜ " : `${board[y][x]} `;
            } else {
                display += "⬛ ";
            }
        }
        display += "\n";
    }

    return new EmbedBuilder()
        .setTitle("🟢 Minesweeper Game")
        .setDescription(`**Player:** ${user.username}\n\n${display}`)
        .setColor("Green")
        .setFooter({ text: "Use !ms reveal x y or !ms flag x y to play!" });
}

function createGameOverEmbed(board, user) {
    let display = "";
    for (let y = 0; y < board.length; y++) {
        for (let x = 0; x < board.length; x++) {
            display += board[y][x] === "💣" ? "💣 " : `${board[y][x]} `;
        }
        display += "\n";
    }

    return new EmbedBuilder()
        .setTitle("💥 Game Over!")
        .setDescription(`**${user.username} hit a mine!**\n\n${display}`)
        .setColor("Red")
        .setFooter({ text: "Use !ms start to play again!" });
}
