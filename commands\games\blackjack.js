const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Row<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle, MessageFlags } = require("discord.js");

module.exports = {
    name: "blackjack",
    aliases: ["bj"],
    category: "game",
    description: "Play Blackjack against another player or the dealer!",
    usePrefix: true,
    isEverywhere: false,
    async execute(message) {
        const mentionedUser = message.mentions.users.first();
        const player1 = message.author;
        const isBotOpponent = mentionedUser?.bot;
        
        // Determine opponent
        const player2 = mentionedUser && !isBotOpponent ? mentionedUser : null;
        const vsDealer = !player2 || isBotOpponent;

        const deck = shuffleDeck(createDeck());
        const hands = {
            [player1.id]: [deck.pop(), deck.pop()],
            [vsDealer ? "dealer" : player2?.id]: [deck.pop(), deck.pop()]
        };

        let currentPlayer = player1.id;
        let isGameOver = false;

        const gameMessage = await message.reply({
            embeds: [createGameEmbed(hands, currentPlayer, false, player1, player2, vsDealer)],
            components: [gameButtons()]
        });

        const collector = gameMessage.createMessageComponentCollector({
            filter: i => i.customId === 'rules' || i.user.id === currentPlayer || (player2 && i.user.id === player2.id),
            time: 300000
        });

        collector.on("collect", async interaction => {
            if (interaction.user.id !== currentPlayer) {
                return interaction.reply({ content: "⛔ It's not your turn!", flags: MessageFlags.Ephemeral });
            }

            if (interaction.customId === "rules") {
                return interaction.reply({
                    embeds: [rulesEmbed()],
                    flags: MessageFlags.Ephemeral
                });
            }

            if (interaction.customId === "hit") {
                hands[currentPlayer].push(deck.pop());
                
                if (calculateHandValue(hands[currentPlayer]) > 21) {
                    isGameOver = true;
                    collector.stop("busted");
                } else if (player2) {
                    currentPlayer = currentPlayer === player1.id ? player2.id : player1.id;
                }

                await interaction.update({
                    embeds: [createGameEmbed(hands, currentPlayer, false, player1, player2, vsDealer)],
                    components: isGameOver ? [] : [gameButtons()]
                });
            }

            if (interaction.customId === "stand") {
                if (player2) {
                    currentPlayer = currentPlayer === player1.id ? player2.id : player1.id;
                    await interaction.update({
                        embeds: [createGameEmbed(hands, currentPlayer, false, player1, player2, vsDealer)],
                        components: [gameButtons()]
                    });
                } else {
                    // Dealer's turn
                    const dealerHand = hands["dealer"];
                    while (calculateHandValue(dealerHand) < 17) {
                        dealerHand.push(deck.pop());
                    }
                    isGameOver = true;
                    collector.stop("dealer-turn");
                }
            }

            if (interaction.customId === "stop") {
                isGameOver = true;
                collector.stop("stopped");
                await interaction.update({ content: "🛑 Game stopped!", components: [] });
            }
        });

        collector.on("end", async () => {
            await gameMessage.edit({
                embeds: [createGameEmbed(hands, currentPlayer, true, player1, player2, vsDealer)],
                components: []
            });
        });
    }
};

function rulesEmbed() {
    return new EmbedBuilder()
        .setTitle("📖 Blackjack Rules")
        .setColor("#0099ff")
        .addFields(
            { name: "Objective", value: "Get cards totaling closer to 21 than the dealer without going over." },
            { name: "Card Values", value: "Number cards = face value\nFace cards = 10\nAce = 1 or 11" },
            { name: "Game Flow", value: "1. Players get 2 cards\n2. Hit to take another card\n3. Stand to keep your total\n4. Dealer reveals cards after all players stand" },
            { name: "Winning", value: "- Beat the dealer's total without busting\n- Blackjack (Ace + 10) beats 21\n- Tie = Push (no win/lose)" }
        )
        .setFooter({ text: "Good luck and have fun!" });
}

function createDeck() {
    const suits = ["♠", "♥", "♦", "♣"];
    const values = ["A", "2", "3", "4", "5", "6", "7", "8", "9", "10", "J", "Q", "K"];
    return suits.flatMap(suit => values.map(value => ({ value, suit })));
}

function shuffleDeck(deck) {
    for (let i = deck.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [deck[i], deck[j]] = [deck[j], deck[i]];
    }
    return deck;
}

function calculateHandValue(hand) {
    if (!hand) return 0;
    let value = 0;
    let aces = 0;

    for (const card of hand) {
        if (["J", "Q", "K"].includes(card.value)) {
            value += 10;
        } else if (card.value === "A") {
            value += 11;
            aces++;
        } else {
            value += parseInt(card.value);
        }
    }

    while (value > 21 && aces > 0) {
        value -= 10;
        aces--;
    }
    return value;
}

function createGameEmbed(hands, currentPlayer, revealDealer, player1, player2, vsDealer) {
    const p1Hand = hands[player1.id];
    const p1Value = calculateHandValue(p1Hand);
    
    const opponentKey = vsDealer ? "dealer" : player2?.id;
    const opponentHand = hands[opponentKey];
    const opponentValue = calculateHandValue(opponentHand);

    // Build display strings
    const p1Display = `${p1Hand.map(c => `${c.value}${c.suit}`).join(" ")} (${p1Value})`;
    
    let opponentDisplay;
    if (vsDealer) {
        opponentDisplay = revealDealer ? 
            `${opponentHand.map(c => `${c.value}${c.suit}`).join(" ")} (${opponentValue})` :
            `${opponentHand[0].value}${opponentHand[0].suit} ❓`;
    } else {
        opponentDisplay = `${opponentHand.map(c => `${c.value}${c.suit}`).join(" ")} (${opponentValue})`;
    }

    // Determine status message
    let status;
    if (revealDealer) {
        status = getGameStatus(p1Value, opponentValue, player1, vsDealer ? "Dealer" : player2?.username);
    } else {
        status = `Current Turn: ${currentPlayer === player1.id ? player1.username : vsDealer ? "Dealer" : player2.username}`;
    }

    return new EmbedBuilder()
        .setTitle("♠️♥️ BLACKJACK ♦️♣️")
        .setColor(revealDealer ? "#FFA500" : "#2F3136")
        .addFields(
            { name: `${player1.username}'s Hand`, value: p1Display, inline: false },
            { name: `${vsDealer ? "Dealer" : player2?.username}'s Hand`, value: opponentDisplay, inline: false },
            { name: "Game Status", value: status }
        )
        .setFooter({ text: revealDealer ? "Game Over" : "Playing..." });
}

function getGameStatus(p1Value, p2Value, player1, opponentName) {
    if (p1Value > 21) return `💥 ${player1.username} Bust! ${opponentName} Wins!`;
    if (p2Value > 21) return `🎉 ${opponentName} Bust! ${player1.username} Wins!`;
    if (p1Value > p2Value) return `🏆 ${player1.username} Wins!`;
    if (p1Value < p2Value) return `😢 ${opponentName} Wins!`;
    return "🤝 It's a Draw!";
}

// Update game buttons to include rules
function gameButtons() {
    return new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId("hit")
            .setLabel("Hit")
            .setStyle(ButtonStyle.Success)
            .setEmoji("⬇️"),
        new ButtonBuilder()
            .setCustomId("stand")
            .setLabel("Stand")
            .setStyle(ButtonStyle.Danger)
            .setEmoji("✋"),
        new ButtonBuilder()
            .setCustomId("stop")
            .setLabel("Stop")
            .setStyle(ButtonStyle.Secondary)
            .setEmoji("⏹️"),
        new ButtonBuilder()
            .setCustomId("rules")
            .setLabel("Rules")
            .setStyle(ButtonStyle.Primary)
            .setEmoji("📖")
    );
}