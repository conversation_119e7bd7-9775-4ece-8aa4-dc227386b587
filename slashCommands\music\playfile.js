const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er, Embed<PERSON><PERSON><PERSON>, PermissionsBitField, MessageFlags } = require('discord.js');
const { useMainPlayer, QueryType } = require('discord-player');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('playfile')
        .setDescription('Play an audio file')
        .addAttachmentOption(option =>
            option.setName('file')
                .setDescription('Upload an audio file')
                .setRequired(true)
        ),

    async execute(interaction) {
        const player = useMainPlayer();
        const voiceChannel = interaction.member.voice.channel;

        if (!voiceChannel) {
            return interaction.reply({
                embeds: [new EmbedBuilder()
                    .setDescription('❌ **You need to be in a voice channel!**')
                    .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        const botPermissions = voiceChannel.permissionsFor(interaction.guild.members.me);
        if (!botPermissions?.has(PermissionsBitField.Flags.Connect)) {
            return interaction.reply({
                embeds: [new EmbedBuilder()
                    .setDescription('❌ **I need `CONNECT` permissions to join the channel!**')
                    .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }
        if (!botPermissions?.has(PermissionsBitField.Flags.Speak)) {
            return interaction.reply({
                embeds: [new EmbedBuilder()
                    .setDescription('❌ **I need `SPEAK` permissions to play music!**')
                    .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        const file = interaction.options.getAttachment('file');

        // Validate if file is an audio file
        if (!file.contentType?.startsWith('audio/') && !/\.(mp3|wav|ogg|flac)$/i.test(file.name)) {
            return interaction.reply({
                embeds: [new EmbedBuilder()
                    .setDescription('❌ **Please upload a valid audio file!**')
                    .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        await interaction.deferReply();

        try {
            const { track } = await player.play(voiceChannel, file.url, {
                searchEngine: QueryType.FILE,
                nodeOptions: {
                    metadata: interaction.channel,
                    volume: 100,
                    leaveOnEnd: false,
                    bufferingTimeout: 15000,
                    selfDeaf: false,
                }
            });

            return interaction.editReply({
                embeds: [new EmbedBuilder()
                    .setTitle('🎵 Added to queue')
                    .setDescription(`**${track.title || 'Uploaded audio file'}**`)
                    .setColor('#00FF00')
                    .setFooter({ text: `Requested by ${interaction.user.tag}` })
                ]
            });
        } catch (error) {
            console.error('Error playing file:', error);
            return interaction.editReply({
                embeds: [new EmbedBuilder()
                    .setDescription('❌ **Failed to play the uploaded file.**')
                    .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }
    }
};
