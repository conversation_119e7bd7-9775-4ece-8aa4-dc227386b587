const { EmbedBuilder, MessageFlags } = require("discord.js");

module.exports = {
    customId: "helpSelect=",
    async execute(client, interaction, id) {
        if (id !== "help") return; // Pastikan hanya menangani select menu dari help command

        const selectedCategory = interaction.values[0];

        const excludedCategories = ["nonprefix"];
        const filteredCommands = client.textCommands.filter(
            (cmd) => !excludedCategories.includes(cmd.category)
        );

        const categoryEmojis = {
            anime: "🎴",
            utility: "🛠️",
            fun: "🎉",
            game: "🎮",
            misc: "*️⃣",
            music: "🎵",
            custom: "⭐"
        };

        const commandsInCategory = filteredCommands
            .filter((cmd) => cmd.category === selectedCategory)
            .map((cmd) => `\`${cmd.name}\``)
            .join(", ");

        const embed = new EmbedBuilder()
            .setColor("#00aaff")
            .setTitle(`${categoryEmojis[selectedCategory] || ""} ${selectedCategory.toUpperCase()} Commands`)
            .setDescription(commandsInCategory || "No commands available.")
            .setFooter({ text: `Requested by ${interaction.user.username}`, iconURL: interaction.user.displayAvatarURL() });

        await interaction.reply({
            embeds: [embed],
            flags: MessageFlags.Ephemeral,
        });
    }
};
