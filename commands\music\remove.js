const { EmbedBuilder } = require('discord.js');
const fs = require('fs');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

function deleteFile(filePath, retries = 3, delay = 1000) {
    if (!filePath || !fs.existsSync(filePath)) return;
    
    const attemptDelete = (attempt) => {
        fs.unlink(filePath, (err) => {
            if (err) {
                if (err.code === 'EBUSY' && attempt < retries) {
                    console.log(`File ${filePath} is busy, retrying in ${delay}ms... (attempt ${attempt + 1}/${retries})`);
                    setTimeout(() => attemptDelete(attempt + 1), delay);
                } else if (err.code === 'ENOENT') {
                    return;
                } else {
                    console.error(`Error deleting file ${filePath} after ${attempt} attempts:`, err.message);
                }
            }
        });
    };
    
    attemptDelete(1);
}

function parseRemoveInput(input, queueLength) {
    // Handle different formats: "3", "2-5", "last", "first"
    input = input.toLowerCase().trim();
    
    if (input === 'last') {
        return [queueLength];
    } else if (input === 'first') {
        return [2]; // Position 1 is currently playing, so first in queue is position 2
    } else if (input.includes('-')) {
        // Range format: "2-5"
        const [start, end] = input.split('-').map(n => parseInt(n.trim()));
        if (isNaN(start) || isNaN(end) || start < 1 || end < start || end > queueLength) {
            return null;
        }
        const positions = [];
        for (let i = start; i <= end; i++) {
            positions.push(i);
        }
        return positions;
    } else {
        // Single position
        const position = parseInt(input);
        if (isNaN(position) || position < 1 || position > queueLength) {
            return null;
        }
        return [position];
    }
}

module.exports = {
    name: 'remove',
    description: 'Remove song(s) from the queue',
    aliases: ['rm', 'delete', 'del'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || serverQueue.songs.length === 0) {
            const emptyQueueEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Empty Queue')
                .setDescription('The music queue is empty!')
                .setTimestamp();
            return message.reply({ embeds: [emptyQueueEmbed] });
        }

        if (args.length === 0) {
            const helpEmbed = new EmbedBuilder()
                .setColor('#FFA500')
                .setTitle('🗑️ Remove Command Help')
                .setDescription('**Usage:** `remove <position(s)>` or `remove @user`\n\n**Examples:**\n• `remove 3` - Remove song at position 3\n• `remove 2-5` - Remove songs 2 through 5\n• `remove last` - Remove last song\n• `remove first` - Remove first song in queue\n• `remove @username` - Remove all songs by user')
                .addFields({
                    name: 'Current Queue',
                    value: `${serverQueue.songs.length} song${serverQueue.songs.length !== 1 ? 's' : ''} in queue`,
                    inline: true
                })
                .setTimestamp();
            return message.reply({ embeds: [helpEmbed] });
        }

        const input = args.join(' ');

        // Check if removing by user mention
        if (message.mentions.users.size > 0) {
            const targetUser = message.mentions.users.first();
            const userTag = targetUser.tag;
            
            const songsToRemove = [];
            for (let i = 1; i < serverQueue.songs.length; i++) { // Skip currently playing song (index 0)
                if (serverQueue.songs[i].requestedBy === userTag) {
                    songsToRemove.push({
                        index: i,
                        song: serverQueue.songs[i]
                    });
                }
            }

            if (songsToRemove.length === 0) {
                const noSongsEmbed = new EmbedBuilder()
                    .setColor('#FFA500')
                    .setTitle('⚠️ No Songs Found')
                    .setDescription(`No songs found in the queue requested by ${targetUser}`)
                    .setTimestamp();
                return message.reply({ embeds: [noSongsEmbed] });
            }

            // Remove songs (in reverse order to maintain indices)
            songsToRemove.reverse().forEach(item => {
                if (item.song.filePath) {
                    deleteFile(item.song.filePath);
                }
                serverQueue.songs.splice(item.index, 1);
                // Also remove from originalQueue for loop functionality
                const originalIndex = serverQueue.originalQueue.findIndex(s => s.id === item.song.id && s.url === item.song.url);
                if (originalIndex > -1) {
                    serverQueue.originalQueue.splice(originalIndex, 1);
                }
            });

            const removedEmbed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('🗑️ Songs Removed')
                .setDescription(`Removed **${songsToRemove.length}** song${songsToRemove.length !== 1 ? 's' : ''} requested by ${targetUser}`)
                .setFooter({ text: `Queue now has ${serverQueue.songs.length} song${serverQueue.songs.length !== 1 ? 's' : ''}` })
                .setTimestamp();

            return message.reply({ embeds: [removedEmbed] });
        }

        // Parse position-based removal
        const positions = parseRemoveInput(input, serverQueue.songs.length);

        if (!positions) {
            const invalidEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Invalid Position')
                .setDescription(`Invalid position format. Use a number between 1 and ${serverQueue.songs.length}, a range like "2-5", or "first"/"last"`)
                .setTimestamp();
            return message.reply({ embeds: [invalidEmbed] });
        }

        // Check if trying to remove currently playing song
        if (positions.includes(1)) {
            const currentlyPlayingEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Cannot Remove')
                .setDescription('Cannot remove the currently playing song! Use `skip` instead.')
                .setTimestamp();
            return message.reply({ embeds: [currentlyPlayingEmbed] });
        }

        // Get songs to remove
        const songsToRemove = positions.map(pos => ({
            position: pos,
            song: serverQueue.songs[pos - 1] // Convert to 0-based index
        })).filter(item => item.song); // Filter out invalid positions

        if (songsToRemove.length === 0) {
            const noSongsEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ No Valid Songs')
                .setDescription('No valid songs found at the specified positions.')
                .setTimestamp();
            return message.reply({ embeds: [noSongsEmbed] });
        }

        // Remove songs (in reverse order to maintain indices)
        songsToRemove.sort((a, b) => b.position - a.position).forEach(item => {
            if (item.song.filePath) {
                deleteFile(item.song.filePath);
            }
            serverQueue.songs.splice(item.position - 1, 1);
            // Also remove from originalQueue for loop functionality
            const originalIndex = serverQueue.originalQueue.findIndex(s => s.id === item.song.id && s.url === item.song.url);
            if (originalIndex > -1) {
                serverQueue.originalQueue.splice(originalIndex, 1);
            }
        });

        // Create success embed
        const removedEmbed = new EmbedBuilder()
            .setColor('#00FF00')
            .setTitle('🗑️ Songs Removed')
            .setTimestamp();

        if (songsToRemove.length === 1) {
            const removedSong = songsToRemove[0].song;
            removedEmbed.setDescription(`Removed: **[${removedSong.title}](${removedSong.displayUrl || removedSong.url})**`)
                .setThumbnail(removedSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=Audio')
                .addFields({
                    name: 'Requested by',
                    value: removedSong.requestedBy || 'Unknown',
                    inline: true
                });
        } else {
            removedEmbed.setDescription(`Removed **${songsToRemove.length}** songs from the queue`);
        }

        removedEmbed.setFooter({ text: `Queue now has ${serverQueue.songs.length} song${serverQueue.songs.length !== 1 ? 's' : ''}` });

        message.reply({ embeds: [removedEmbed] });
    },
};
