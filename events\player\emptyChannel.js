const { EmbedBuilder } = require('discord.js');

module.exports = (player, queue) => {
    try {
        if (!queue.metadata || !queue.metadata.send) return;

        const embed = new EmbedBuilder()
            .setTitle("🚪 Empty Voice Channel")
            .setDescription("Everyone has left the voice channel, so the music has stopped. Join back and add songs to resume! 🎶")
            .setColor("#FFA500") // Orange color
            .setTimestamp();

        queue.metadata.send({ embeds: [embed] }).catch(console.error);
    } catch (error) {
        console.error("Error sending empty channel embed:", error);
    }
};
