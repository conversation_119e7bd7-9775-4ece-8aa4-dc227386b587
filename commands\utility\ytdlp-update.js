/**
 * yt-dlp Update Command
 * Provides manual control over the yt-dlp auto-updater
 */

const { 
    createThemedEmbed, 
    createStatusIndicator,
    EMOJIS 
} = require('../../utils/embedTheme');

module.exports = {
    name: 'ytdlp-update',
    description: 'Manage yt-dlp updates - check, update, or view status',
    aliases: ['ytdlp', 'update-ytdlp', 'yt-update'],
    category: 'utility',
    usePrefix: true,
    isEverywhere: false,
    ownerOnly: true, // Only bot owner can use this command
    
    async execute(message, args) {
        try {
            // Import the updater (lazy loading to avoid startup issues)
            const { updater } = require('../../plugin/javascript/ytdlp-updater');
            
            const subcommand = args[0]?.toLowerCase() || 'status';
            
            switch (subcommand) {
                case 'check':
                    await this.handleCheck(message, updater);
                    break;
                    
                case 'update':
                case 'force':
                    await this.handleUpdate(message, updater, subcommand === 'force');
                    break;
                    
                case 'status':
                    await this.handleStatus(message, updater);
                    break;
                    
                case 'help':
                    await this.handleHelp(message);
                    break;
                    
                default:
                    await this.handleHelp(message);
                    break;
            }
            
        } catch (error) {
            console.error('❌ yt-dlp update command error:', error);
            
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Update Command Failed`)
                .setDescription(`${createStatusIndicator('error', 'Failed to execute update command')}\n\n**Error:** ${error.message}`)
                .addFields({
                    name: `${EMOJIS.INFO} Troubleshooting`,
                    value: `${EMOJIS.SPARKLES} Check if the updater plugin is loaded\n${EMOJIS.LINK} Ensure internet connection is stable\n${EMOJIS.LIGHTNING} Try restarting the bot`,
                    inline: false
                });
                
            await message.reply({ embeds: [errorEmbed] });
        }
    },
    
    async handleCheck(message, updater) {
        const checkingEmbed = createThemedEmbed('info')
            .setTitle(`${EMOJIS.SEARCH} Checking for Updates`)
            .setDescription(`${createStatusIndicator('info', 'Checking GitHub for latest yt-dlp version...')}\n\n${EMOJIS.TIMER} This may take a few seconds`);
            
        const statusMessage = await message.reply({ embeds: [checkingEmbed] });
        
        try {
            const result = await updater.checkForUpdates(false);
            
            if (result.success) {
                const successEmbed = createThemedEmbed('success')
                    .setTitle(`${EMOJIS.SUCCESS} Update Check Complete`)
                    .setDescription(`${createStatusIndicator('success', result.message)}\n\n**Current Version:** ${result.version}`)
                    .addFields({
                        name: `${EMOJIS.INFO} Next Steps`,
                        value: result.message.includes('up to date') 
                            ? `${EMOJIS.SPARKLES} Your yt-dlp is current!`
                            : `${EMOJIS.LIGHTNING} Use \`ytdlp-update update\` to install the latest version`,
                        inline: false
                    });
                    
                await statusMessage.edit({ embeds: [successEmbed] });
            } else {
                throw new Error(result.error);
            }
            
        } catch (error) {
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Update Check Failed`)
                .setDescription(`${createStatusIndicator('error', 'Could not check for updates')}\n\n**Error:** ${error.message}`);
                
            await statusMessage.edit({ embeds: [errorEmbed] });
        }
    },
    
    async handleUpdate(message, updater, force) {
        const updatingEmbed = createThemedEmbed('info')
            .setTitle(`${EMOJIS.GEAR} ${force ? 'Force ' : ''}Updating yt-dlp`)
            .setDescription(`${createStatusIndicator('info', 'Downloading and installing latest version...')}\n\n${EMOJIS.TIMER} This may take a few minutes\n${EMOJIS.INFO} A backup will be created automatically`);
            
        const statusMessage = await message.reply({ embeds: [updatingEmbed] });
        
        try {
            const result = await updater.manualUpdate(force);
            
            if (result.success) {
                const successEmbed = createThemedEmbed('success')
                    .setTitle(`${EMOJIS.SUCCESS} Update Complete`)
                    .setDescription(`${createStatusIndicator('success', result.message)}\n\n**New Version:** ${result.version}`)
                    .addFields({
                        name: `${EMOJIS.SPARKLES} What's Next`,
                        value: `${EMOJIS.MUSIC_NOTE} Your music commands will now use the latest yt-dlp\n${EMOJIS.LIGHTNING} Improved site support and bug fixes\n${EMOJIS.HEART} Backup was created automatically`,
                        inline: false
                    });
                    
                await statusMessage.edit({ embeds: [successEmbed] });
            } else {
                throw new Error(result.error);
            }
            
        } catch (error) {
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Update Failed`)
                .setDescription(`${createStatusIndicator('error', 'Could not update yt-dlp')}\n\n**Error:** ${error.message}`)
                .addFields({
                    name: `${EMOJIS.INFO} Recovery`,
                    value: `${EMOJIS.HEART} Previous version was restored from backup\n${EMOJIS.SPARKLES} Your music functionality is still working`,
                    inline: false
                });
                
            await statusMessage.edit({ embeds: [errorEmbed] });
        }
    },
    
    async handleStatus(message, updater) {
        try {
            const status = updater.getStatus();
            const currentVersion = await updater.getCurrentVersion();
            
            const statusEmbed = createThemedEmbed('info')
                .setTitle(`${EMOJIS.INFO} yt-dlp Updater Status`)
                .setDescription(`${createStatusIndicator('info', 'Current updater configuration and status')}`)
                .addFields(
                    {
                        name: `${EMOJIS.GEAR} Current Version`,
                        value: `${EMOJIS.MUSIC_NOTE} ${currentVersion || 'Unknown/Not installed'}`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.TIMER} Auto-Update`,
                        value: `${status.autoUpdateEnabled ? EMOJIS.SUCCESS + ' Enabled' : EMOJIS.ERROR + ' Disabled'}`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.CLOCK} Check Interval`,
                        value: `${EMOJIS.LIGHTNING} Every ${status.checkInterval / (60 * 60 * 1000)} hours`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.FILE} Binary Status`,
                        value: `${EMOJIS.MUSIC_NOTE} Exists: ${status.ytdlpExists ? EMOJIS.SUCCESS : EMOJIS.ERROR}\n${EMOJIS.HEART} Backup: ${status.backupExists ? EMOJIS.SUCCESS : EMOJIS.ERROR}`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.FOLDER} Installation Path`,
                        value: `${EMOJIS.LINK} \`${status.binPath}\``,
                        inline: false
                    }
                );
                
            await message.reply({ embeds: [statusEmbed] });
            
        } catch (error) {
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Status Check Failed`)
                .setDescription(`${createStatusIndicator('error', 'Could not retrieve updater status')}\n\n**Error:** ${error.message}`);
                
            await message.reply({ embeds: [errorEmbed] });
        }
    },
    
    async handleHelp(message) {
        const helpEmbed = createThemedEmbed('info')
            .setTitle(`${EMOJIS.INFO} yt-dlp Update Commands`)
            .setDescription(`${createStatusIndicator('info', 'Manage your yt-dlp installation')}\n\n**Auto-updater keeps yt-dlp current with latest features and site support!**`)
            .addFields(
                {
                    name: `${EMOJIS.SEARCH} Check for Updates`,
                    value: `${EMOJIS.LIGHTNING} \`ytdlp-update check\` - Check if updates are available`,
                    inline: false
                },
                {
                    name: `${EMOJIS.GEAR} Update yt-dlp`,
                    value: `${EMOJIS.SPARKLES} \`ytdlp-update update\` - Update to latest version\n${EMOJIS.FIRE} \`ytdlp-update force\` - Force update even if current`,
                    inline: false
                },
                {
                    name: `${EMOJIS.INFO} View Status`,
                    value: `${EMOJIS.MUSIC_NOTE} \`ytdlp-update status\` - Show current status and config`,
                    inline: false
                },
                {
                    name: `${EMOJIS.SPARKLES} Features`,
                    value: `${EMOJIS.TIMER} Automatic daily update checks\n${EMOJIS.HEART} Automatic backups before updates\n${EMOJIS.LIGHTNING} Support for 1000+ video sites\n${EMOJIS.GEAR} Manual update control`,
                    inline: false
                }
            );
            
        await message.reply({ embeds: [helpEmbed] });
    }
};
