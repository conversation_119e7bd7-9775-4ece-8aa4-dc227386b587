const {customCommandDB} = require('../../database/manager')

module.exports = {
    name: 'delcommand',
    aliases: ['deletecmd', 'removecmd'],
    category: 'custom',
    description: 'Delete a custom command.\nFormat: `!delcommand <name>`',
    usePrefix: true,
    isEverywhere: false,

    async execute(message, args) {
        const commandName = args.shift()?.toLowerCase();
        if (!commandName) {
            return message.reply('❌ Please provide the name of the command you want to delete!\nFormat: `!delcommand <name>`');
        }

        const exists = await customCommandDB.get(`custom_${commandName}`);
        if (!exists) return message.reply('❌ Command not found.');

        await customCommandDB.delete(`custom_${commandName}`);
        message.reply(`✅ Command **${commandName}** has been deleted.`);
    }
};
