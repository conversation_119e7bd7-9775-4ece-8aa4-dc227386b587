const {customCommandDB} = require('../../database/manager')

module.exports = {
    name: 'editcommand',
    aliases: ['editcmd'],
    category: 'custom',
    description: 'Edit a custom command.\nFormat: `!editcommand <name> <new_response>`\nAdd `--noprefix` to make it without a prefix.\nAdd `--isEverywhere` to trigger it anywhere.',
    usePrefix: true,
    isEverywhere: false,

    async execute(message, args) {
        const useNoPrefix = args.includes('--noprefix');
        const isEverywhere = args.includes('--isEverywhere');

        // Remove flags from args
        args = args.filter(arg => arg !== '--noprefix' && arg !== '--isEverywhere');

        const commandName = args.shift()?.toLowerCase();
        const newResponse = args.join(" ");

        if (!commandName || !newResponse) {
            return message.reply('❌ Incorrect format! Use: `!editcommand <name> <new_response>`\nAdd `--noprefix` if you don\'t want to use a prefix.\nAdd `--isEverywhere` to trigger it anywhere.');
        }

        const exists = await customCommandDB.get(`custom_${commandName}`);
        if (!exists) {
            return message.reply(`❌ Command **${commandName}** not found.`);
        }

        await customCommandDB.set(`custom_${commandName}`, { response: newResponse, usePrefix: !useNoPrefix, isEverywhere });
        message.reply(`✅ Command **${commandName}** has been updated!\nIt can now be used with: \`${useNoPrefix ? commandName : `!${commandName}`}\`${isEverywhere ? ' (🌍 Everywhere)' : ''}`);
    }
};
