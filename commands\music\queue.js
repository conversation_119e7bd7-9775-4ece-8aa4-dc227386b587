const { ActionRowBuilder, ButtonBuilder, ButtonStyle, MessageFlags } = require('discord.js');
const {
    createThemedEmbed,
    formatSongTitle,
    createStatusIndicator,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

module.exports = {
    name: 'queue',
    description: 'Display the current music queue with pagination',
    aliases: ['q', 'list'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || serverQueue.songs.length === 0) {
            const emptyQueueEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Empty Queue`)
                .setDescription(`${createStatusIndicator('error', 'The music queue is empty!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to add songs to the queue.`)
                .addFields({
                    name: `${EMOJIS.SPARKLES} Quick Start`,
                    value: `${EMOJIS.PLAY} \`play <song name>\` - Search and play\n${EMOJIS.MUSIC_NOTE} \`play <YouTube URL>\` - Play from URL`,
                    inline: false
                });
            return message.reply({ embeds: [emptyQueueEmbed] });
        }

        const songsPerPage = 10;
        const totalPages = Math.ceil(serverQueue.songs.length / songsPerPage);
        let currentPage = 1;

        // Parse page number from args if provided
        if (args.length > 0) {
            const pageArg = parseInt(args[0]);
            if (!isNaN(pageArg) && pageArg >= 1 && pageArg <= totalPages) {
                currentPage = pageArg;
            }
        }

        function createQueueEmbed(page) {
            const startIndex = (page - 1) * songsPerPage;
            const endIndex = Math.min(startIndex + songsPerPage, serverQueue.songs.length);

            let queueDescription = '';

            for (let i = startIndex; i < endIndex; i++) {
                const song = serverQueue.songs[i];
                const position = i + 1;
                const isCurrentSong = i === 0 && serverQueue.playing;

                // Add autoplay and playlist indicators
                const autoplayIndicator = song.fromAutoplay ? `${EMOJIS.SPARKLES} ` : '';
                const playlistIndicator = song.fromPlaylist ? `${EMOJIS.PLAYLIST} ` : '';

                if (isCurrentSong) {
                    // Match slash command format for current track
                    queueDescription += `${EMOJIS.MUSIC_NOTE} **[NOW PLAYING]** ${autoplayIndicator}${playlistIndicator}${formatSongTitle(song.title, song.displayUrl || song.url, true)}\n`;
                } else {
                    // Match slash command format for queued tracks
                    queueDescription += `${position}. ${autoplayIndicator}${playlistIndicator}${formatSongTitle(song.title, song.displayUrl || song.url)}\n`;
                }
            }

            // Calculate queued tracks (excluding current song)
            const queuedTracks = Math.max(0, serverQueue.songs.length - 1);

            const embed = createThemedEmbed('queue')
                .setTitle(`${EMOJIS.QUEUE} Current Queue (Page ${page}/${totalPages})`)
                .setDescription(queueDescription || `${EMOJIS.INFO} No songs to display on this page.`)
                .setThumbnail(message.guild.iconURL({ dynamic: true }) || null)
                .setFooter({
                    text: `Total songs: ${serverQueue.songs.length} | Queued: ${queuedTracks}`
                });

            // Add current status if on first page
            if (page === 1 && serverQueue.currentSong) {
                const statusText = serverQueue.playing ? 'Playing' : 'Paused';
                const statusEmoji = serverQueue.playing ? EMOJIS.PLAY : EMOJIS.PAUSE;
                const autoplayStatus = serverQueue.autoplay ? ` • ${EMOJIS.SPARKLES} Autoplay: ON` : '';
                embed.addFields({
                    name: `${statusEmoji} Current Status`,
                    value: `${statusText} • Volume: ${serverQueue.volume || 50}%${serverQueue.loopMode && serverQueue.loopMode !== 'off' ? ` • Loop: ${serverQueue.loopMode}` : ''}${autoplayStatus}`,
                    inline: false
                });
            }

            return embed;
        }

        function createButtons(page) {
            const row = new ActionRowBuilder();

            // Previous button (match slash command style)
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('queue_prev')
                    .setLabel('⬅️')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(page <= 1)
            );

            // Page indicator
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('queue_page')
                    .setLabel(`${page}/${totalPages}`)
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(true)
            );

            // Next button (match slash command style)
            row.addComponents(
                new ButtonBuilder()
                    .setCustomId('queue_next')
                    .setLabel('➡️')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(page >= totalPages)
            );

            return row;
        }

        const embed = createQueueEmbed(currentPage);
        const components = totalPages > 1 ? [createButtons(currentPage)] : [];

        const response = await message.reply({
            embeds: [embed],
            components: components
        });

        // Only add button collector if there are multiple pages
        if (totalPages > 1) {
            const collector = response.createMessageComponentCollector({
                time: 60000 // 1 minute
            });

            collector.on('collect', async (interaction) => {
                if (interaction.user.id !== message.author.id) {
                    return interaction.reply({
                        content: 'Only the command user can use these buttons!',
                        flags: MessageFlags.Ephemeral
                    });
                }

                if (interaction.customId === 'queue_prev' && currentPage > 1) {
                    currentPage--;
                } else if (interaction.customId === 'queue_next' && currentPage < totalPages) {
                    currentPage++;
                }

                const newEmbed = createQueueEmbed(currentPage);
                const newComponents = [createButtons(currentPage)];

                await interaction.update({
                    embeds: [newEmbed],
                    components: newComponents
                });
            });

            collector.on('end', () => {
                // Disable all buttons when collector ends (match slash command style)
                const disabledRow = new ActionRowBuilder()
                    .addComponents(
                        new ButtonBuilder()
                            .setCustomId('queue_prev')
                            .setLabel('⬅️')
                            .setStyle(ButtonStyle.Primary)
                            .setDisabled(true),
                        new ButtonBuilder()
                            .setCustomId('queue_page')
                            .setLabel(`${currentPage}/${totalPages}`)
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(true),
                        new ButtonBuilder()
                            .setCustomId('queue_next')
                            .setLabel('➡️')
                            .setStyle(ButtonStyle.Primary)
                            .setDisabled(true)
                    );

                response.edit({ components: [disabledRow] }).catch(console.error);
            });
        }
    },
};
