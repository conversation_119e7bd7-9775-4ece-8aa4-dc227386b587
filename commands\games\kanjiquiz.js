const { ActionRowBuilder, ButtonBuilder, ButtonStyle, EmbedBuilder, MessageFlags } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Load kanji data from local JSON
const kanjiData = JSON.parse(fs.readFileSync(path.join(__dirname, '../../utils/kanji.json'), 'utf-8'));
const kanjiDataList = Object.values(kanjiData.kanjis);


// Shuffle helper
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

module.exports = {
    name: 'kanjiquiz',
    aliases: ['kq', 'kanji'],
    category: 'game',
    description: 'Start a multiple-choice kanji quiz with buttons.',
    usePrefix: true,
    isEverywhere: false,

    async execute(message, args) {
        const loadingMsg = await message.channel.send('Starting Kanji quiz...');

        try {
            const kanjiList = kanjiDataList.filter(k => k.meanings && k.meanings.length > 0);

            if (!kanjiList.length) {
                return loadingMsg.edit('Kanji list is empty. Check your JSON file.');
            }

            let health = 3;
            let score = 0;

            while (health > 0) {
                let correctEntry = kanjiList[Math.floor(Math.random() * kanjiList.length)];
                const correctKanji = correctEntry.kanji;
                const correctAnswer = correctEntry.meanings[0];

                const distractors = [];
                const used = new Set([correctKanji]);

                let attempts = 0;
                while (distractors.length < 3 && attempts < 30) {
                    attempts++;
                    const randomEntry = kanjiList[Math.floor(Math.random() * kanjiList.length)];
                    if (used.has(randomEntry.kanji)) continue;
                    const meaning = randomEntry.meanings[0];
                    if (meaning && meaning !== correctAnswer && !distractors.includes(meaning)) {
                        distractors.push(meaning);
                        used.add(randomEntry.kanji);
                    }
                }

                if (distractors.length < 3) {
                    return loadingMsg.edit('Not enough unique options. Try again.');
                }

                const allOptions = shuffleArray([correctAnswer, ...distractors]);
                const letters = ['A', 'B', 'C', 'D'];
                const correctIndex = allOptions.indexOf(correctAnswer);
                const correctLetter = letters[correctIndex];

                const choices = allOptions.map((opt, i) => `**${letters[i]})** ${opt}`).join('\n');

                const quizEmbed = new EmbedBuilder()
                    .setColor(0x5865F2)
                    .setTitle('Kanji Quiz')
                    .setDescription(`What is the main meaning of this Kanji?\n\n# ${correctKanji}`)
                    .addFields({ name: 'Choices', value: choices })
                    .setFooter({ text: `Health: ${'💖'.repeat(health)} | Score: ${score} | You have 30 seconds` });

                const row = new ActionRowBuilder().addComponents(
                    ...letters.map((letter) =>
                        new ButtonBuilder()
                            .setCustomId(letter)
                            .setLabel(letter)
                            .setStyle(ButtonStyle.Primary)
                    )
                );

                const quizMsg = await loadingMsg.edit({ content: '', embeds: [quizEmbed], components: [row] });

                const collector = quizMsg.createMessageComponentCollector({
                    filter: i => i.user.id === message.author.id,
                    time: 30000
                });

                const countdown = [3, 2, 1];
                const timeoutTimers = countdown.map((n, i) => {
                    return setTimeout(() => {
                        quizMsg.react(['3️⃣', '2️⃣', '1️⃣'][i]).catch(() => {});
                    }, 30000 - (n * 1000));
                });

                let answered = false;

                collector.on('collect', async interaction => {
                    const chosen = interaction.customId;
                    if (chosen === correctLetter) {
                        score++;
                        await interaction.update({
                            content: `✅ Correct! ${correctLetter}) ${correctAnswer}`,
                            embeds: [],
                            components: []
                        });
                        answered = true;
                        collector.stop();
                    } else {
                        health--;
                        if (health === 0) {
                            await interaction.update({
                                content: `💀 No more health! The correct answer was **${correctLetter}) ${correctAnswer}**.`,
                                embeds: [],
                                components: []
                            });
                            answered = true;
                            collector.stop();
                        } else {
                            await interaction.reply({ content: `❌ Wrong! Health left: ${'💖'.repeat(health)}`, flags: MessageFlags.Ephemeral });
                        }
                    }
                });

                await new Promise(resolve => {
                    collector.on('end', async () => {
                        timeoutTimers.forEach(clearTimeout);
                        if (!answered) {
                            health--;
                            const content = health === 0
                                ? `💀 Time's up! The correct answer was **${correctLetter}) ${correctAnswer}**.`
                                : `⏰ Time's up! Health left: ${'💖'.repeat(health)}. Correct answer was **${correctLetter}) ${correctAnswer}**.`;

                            await quizMsg.edit({ content, embeds: [], components: [] }).catch(() => {});
                        }
                        resolve();
                    });
                });
            }

            await message.channel.send(`🎉 Kanji quiz over! Your final score: **${score}**`);
        } catch (err) {
            console.error('Quiz error:', err);
            await loadingMsg.edit('An error occurred while starting the quiz. Try again later.\n' + `Error: ${err.message}`);
        }
    }
};
