module.exports = {
    name: "apa<PERSON>h",
    aliases: ["apa"],
    category: "fun",
    description: "Anda bertanya, bot menjawab",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {

        if (!args.length) {
            return message.reply("Haeh?");
        }
        
        const answers = [
            // Tier 1 (Common - 89%)
            { text: "Iya", weight: 445, tier: 1 },
            { text: "Tidak", weight: 445, tier: 1 },

            // Tier 2 (Uncommon - 10%)
            { text: "Mungkin", weight: 25, tier: 2 },
            { text: "Meragukan", weight: 25, tier: 2 },
            { text: "Tentu saja", weight: 25, tier: 2 },
            { text: "Sudah pasti tidak", weight: 25, tier: 2 },
            { text: "Saya mendukung", weight: 25, tier: 2 },
            { text: "Pasti donk", weight: 25, tier: 2 },
            { text: "Ntahlah", weight: 25, tier: 2 },

            // Tier 3 (Extremely Rare - 0.9%)
            { text: "BIG NO", weight: 0.5, tier: 3 },
            { text: "BIG YES", weight: 0.5, tier: 3 },
            { text: "<PERSON><PERSON>, coba tanya lagi", weight: 0.5, tier: 3 },
            { text: "Kamu nanya ke bot gitu?", weight: 0.5, tier: 3 },
            { text: "Sangat meragukan", weight: 0.5, tier: 3 },
            { text: "I Approve", weight: 0.5, tier: 3 },
            { text: "I Disapprove", weight: 0.5, tier: 3 },
            { text: "はい\n\n- YAGOO", weight: 0.5, tier: 3 },
            { text: "Sou desune..", weight: 0.5, tier: 3 },
            { text: "Tidak tau", weight: 0.5, tier: 3 },
            { text: "Coba tanya yang lain", weight: 0.5, tier: 3 },
            { text: "Si", weight: 0.5, tier: 3 },
            { text: "Nein", weight: 0.5, tier: 3 },

            // Tier 4 (INSANE RARE - 0.09%)
            { text: "⚠️ SYSTEM ERROR. CODE: 0x000F1", weight: 0.09, tier: 4 },
            { text: "null", weight: 0.09, tier: 4 },
            { text: "[object Object]", weight: 0.09, tier: 4 },
            { text: "⣿⣿⣿⣿⣿⣿⣿⣿", weight: 0.09, tier: 4 },
            { text: "Hasil tidak ditemukan.", weight: 0.09, tier: 4 },
            { text: "Kenapa kamu masih nanya?", weight: 0.09, tier: 4 },

            // Tier 5 (MYTHICAL - 0.001%)  
            { text: "🚀 SELAMAT! Kamu dapet jawaban ini! (1 dari 100.000 percobaan)", weight: 0.001, tier: 5 },
            { text: "🔥 Kamu resmi orang paling hoki di dunia!", weight: 0.001, tier: 5 },
            { text: "💀 Kamu telah membuka kutukan pertanyaan ini.", weight: 0.001, tier: 5 },
            { text: "🎉 ROLE RARE UNLOCKED!", weight: 0.001, tier: 5 }
        ];

        // Hitung total bobot
        const totalWeight = answers.reduce((sum, a) => sum + a.weight, 0);

        // Pilih jawaban berdasarkan bobot
        let random = Math.random() * totalWeight;
        let selectedAnswer = answers.find(a => (random -= a.weight) <= 0);

        // Pesan congratulatory berdasarkan tier
        let congratsMessage = "";
        if (selectedAnswer.tier === 2) {
            congratsMessage = "\n🎉 Selamat, kamu dapet jawaban *Uncommon*! 🚀";
        } else if (selectedAnswer.tier === 3) {
            congratsMessage = "\n🔥 WOOOW! Kamu dapet *Extremely Rare*! 🎰✨";
        } else if (selectedAnswer.tier === 4) {
            congratsMessage = "\n💀 SYSTEM ERROR DETECTED. 🚨";
        } else if (selectedAnswer.tier === 5) {
            congratsMessage = "\n🌌 **MYTHICAL DROP!!** Kamu harusnya beli lotre sekarang! 🎟️";
        }

        // Kirim jawaban
        message.reply(`${selectedAnswer.text}${congratsMessage}`);
    }
};
