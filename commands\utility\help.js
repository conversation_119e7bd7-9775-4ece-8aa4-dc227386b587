const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder } = require("discord.js");

module.exports = {
    name: "help",
    aliases: ["commands", "h"],
    category: "utility",
    description: "Displays a list of available commands",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const excludedCategories = ["nonprefix"]; // Categories to exclude
        const prefix = "!"; // Ganti jika prefix berbeda

        const howTo =
            `📌 **How to use the bot:**\n` +
            `- Use all commands with the prefix \`${prefix}\`\n` +
            `- To view commands in a specific category: \`${prefix}help <category>\`\n` +
            `- To view details of a command: \`${prefix}help <command>\`\n` +
            `- To see this command list again: \`${prefix}help\``;

        const categoryEmojis = {
            anime: "🎴",
            utility: "🛠️",
            fun: "🎉",
            game: "🎮",
            misc: "*️⃣",
            music: "🎵",
            custom: "⭐"
            
        };

        const filteredCommands = message.client.textCommands.filter(
            (cmd) => !excludedCategories.includes(cmd.category)
        );

        const categories = [...new Set(filteredCommands.map((cmd) => cmd.category))];

        // Jika tidak ada argumen -> Tampilkan Select Menu
        if (!args[0]) {
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId("helpSelect=help") // Custom ID untuk handler select menu
                .setPlaceholder("Select a category to view its commands")
                .addOptions(
                    categories.map((category) => ({
                        label: category.charAt(0).toUpperCase() + category.slice(1),
                        value: category,
                        emoji: categoryEmojis[category] || "📂",
                    }))
                );

            const row = new ActionRowBuilder().addComponents(selectMenu);

            const embed = new EmbedBuilder()
                .setColor("#00ff99")
                .setTitle("📜 Command List")
                .setDescription(howTo)
                .setFooter({ text: `Requested by ${message.author.username}`, iconURL: message.author.displayAvatarURL() });

            return message.reply({ embeds: [embed], components: [row] });
        }

        const categoryRequested = args[0].toLowerCase();

        // Jika input adalah kategori -> Tampilkan daftar command dalam kategori itu
        if (categories.includes(categoryRequested)) {
            const commandsInCategory = filteredCommands
                .filter((cmd) => cmd.category === categoryRequested)
                .map((cmd) => `\`${cmd.name}\``)
                .join(", ");

            const embed = new EmbedBuilder()
                .setColor("#00aaff")
                .setTitle(`${categoryEmojis[categoryRequested] || ""} ${categoryRequested.toUpperCase()} Commands`)
                .setDescription(commandsInCategory || "No commands in this category.")
                .setFooter({ text: `Requested by ${message.author.username}`, iconURL: message.author.displayAvatarURL() });

            return message.reply({ embeds: [embed] });
        }

        // Jika input adalah nama command atau alias -> Tampilkan detail command
        const command =
            filteredCommands.find((cmd) => cmd.name === args[0]) ||
            filteredCommands.find((cmd) => cmd.aliases && cmd.aliases.includes(args[0]));

        if (!command) {
            return message.reply("❌ Command or category not found.");
        }

        const embed = new EmbedBuilder()
            .setColor("#ff9900")
            .setTitle(`ℹ️ Command: ${command.name}`)
            .addFields(
                { name: "📝 Description", value: command.description || "No description available", inline: false },
                { name: "🔀 Aliases", value: command.aliases.length ? command.aliases.join(", ") : "-", inline: false },
                { name: "📂 Category", value: command.category.charAt(0).toUpperCase() + command.category.slice(1), inline: false }
            )
            .setFooter({ text: `Requested by ${message.author.username}`, iconURL: message.author.displayAvatarURL() });

        return message.reply({ embeds: [embed] });
    },
};
