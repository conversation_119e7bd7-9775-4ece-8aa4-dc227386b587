const { EmbedBuilder } = require("discord.js");
const { inspect } = require("util");

module.exports = {
    name: "code",
    aliases: [],
    category: "misc",
    description: "Menjalankan kode JavaScript melalui bot",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const code = args.join(" ");
        if (!code) {
            return message.reply("⚠️ Harap masukkan kode JavaScript untuk dieksekusi.");
        }

        // Daftar kata kunci yang dilarang untuk mencegah akses tidak sah
        const forbiddenKeywords = ["process", "require", "global", "module", "exports"];

        if (forbiddenKeywords.some((keyword) => code.includes(keyword))) {
            return message.reply("⚠️ Kode mengandung kata-kata terlarang.");
        }

        let output = "";
        const originalConsoleLog = console.log;

        try {
            // Menangkap output dari console.log
            console.log = (...args) => {
                output += args.map(arg => inspect(arg)).join(" ") + "\n";
            };

            // Mengeksekusi kode dalam konteks async
            const result = await (async () => eval(code))();

            // Menentukan output akhir
            const finalOutput = output.trim() || inspect(result, { depth: 1 });
            const truncatedOutput = finalOutput.length > 1000 ? finalOutput.slice(0, 1000) + "..." : finalOutput;

            const embed = new EmbedBuilder()
                .setColor("#00ff00")
                .setTitle("✅ JavaScript Execution")
                .addFields(
                    { name: "📥 Input", value: `\`\`\`js\n${code}\n\`\`\`` },
                    { name: "📤 Output", value: `\`\`\`js\n${truncatedOutput}\n\`\`\`` }
                )
                .setFooter({ text: `Executed by ${message.author.tag}`, iconURL: "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6a/JavaScript-logo.png/900px-JavaScript-logo.png" })
                .setTimestamp();

            await message.channel.send({ embeds: [embed] });

        } catch (error) {
            const errorEmbed = new EmbedBuilder()
                .setColor("#ff0000")
                .setTitle("❌ Execution Error")
                .setDescription(`\`\`\`js\n${error.message}\n\`\`\``)
                .setFooter({ text: `Executed by ${message.author.tag}`, iconURL: "https://upload.wikimedia.org/wikipedia/commons/thumb/6/6a/JavaScript-logo.png/900px-JavaScript-logo.png" })
                .setTimestamp();

            await message.channel.send({ embeds: [errorEmbed] });
        } finally {
            // Mengembalikan console.log ke bentuk aslinya
            console.log = originalConsoleLog;
        }
    },
};
