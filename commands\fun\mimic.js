const { WebhookClient, PermissionsBitField } = require('discord.js');

module.exports = {
    name: 'mimic',
    aliases: ['impersonate'],
    category: 'fun',
    description: 'Mimic the user or the tagged user using a webhook.',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        if (!args.length) return message.reply('Please provide a message to mimic.');

        let targetMember = message.mentions.members.last() || message.member;
        let targetUser = targetMember.user;

        // Ambil konten dari args dan hapus mention terakhir jika ada
        let content = args.join(' ');

        if (message.mentions.members.size) {
            const mentionRegex = new RegExp(`<@!?${targetMember.id}>\\s*$`);
            content = content.replace(mentionRegex, '').trim();
        }

        if (!content) return message.reply('Please provide a message to mimic.');

        if (message.guild && !message.channel.permissionsFor(message.client.user).has(PermissionsBitField.Flags.ManageMessages)) {
            return message.reply('I need the "Manage Messages" permission to delete your message.');
        }

        try {
            await message.delete();

            const webhook = await message.channel.createWebhook({
                name: targetMember.displayName,
                avatar: targetUser.displayAvatarURL({ dynamic: true })
            });

            await webhook.send({
                content: content,
                username: targetMember.displayName,
                avatarURL: targetUser.displayAvatarURL({ dynamic: true })
            });

            setTimeout(async () => {
                await webhook.delete();
            }, 1000);

        } catch (error) {
            console.error('Error creating or sending webhook:', error);
            message.reply('Failed to mimic the user.');
        }
    }
};