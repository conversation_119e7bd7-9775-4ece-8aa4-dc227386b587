const {rikaDB} = require('../../database/manager')
const axios = require("axios");

module.exports = {
    name: "chat",
    aliases: ["gpt"],
    description: "Menggunakan G4F API untuk mendapatkan balasan",
    category: "fun",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        if (!args.length) {
            return message.reply("Kirimkan pesan untuk diproses: `!chat <pesan>`");
        }

        const userMessage = args.join(" ");
        const userId = message.author.id;
        const userName = message.author.username;
        const guildName = message.guild ? message.guild.name : "Direct Message";

        // Ambil riwayat percakapan sebelumnya dari database
        let previousMessages = (await rikaDB.get(`chatHistory_${userId}`)) || [];

        // Hapus riwayat paling lama jika sudah mencapai 40 pesan
        if (previousMessages.length >= 40) {
            previousMessages.shift();
        }

        // Tambahkan pesan pengguna ke riwayat
        previousMessages.push({
            role: "user",
            content: userMessage,
        });

        try {
            // Kirim pesan ke API Python
            const response = await axios.post("http://localhost:5000/chat", {
                messages: [
                    {
                        role: "system",
                        content: `
                        Task:
                        - Chat as naturally as possible with the user.
                        - Simulate human conversation with the user.
                        - Provide reliable sources of information such as Wikipedia.
                        - When users ask for your command list, tell them to type "!help".
                        - Occasionally roast users humorously.
                        - Avoid replying in Spanish unless initiated by the user.
                        - Be fun.

                        Current User:
                        - Name: ${userName}
                        - Server: ${guildName}
                        `
                    },
                    ...previousMessages
                ]
            });

            const reply = response.data;

            // Tambahkan respons bot ke riwayat
            previousMessages.push({
                role: "assistant",
                content: reply,
            });

            // Simpan ke database
            await rikaDB.set(`chatHistory_${userId}`, previousMessages);

            // Kirim balasan ke pengguna
            await message.reply(reply);
        } catch (error) {
            console.error("Error contacting Python API:", error.message);
            await message.reply("Terjadi kesalahan saat memproses pesan Anda.");
        }
    }
};
