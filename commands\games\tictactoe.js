const { <PERSON>Row<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, EmbedBuilder, MessageFlags } = require('discord.js');

module.exports = {
    name: 'tictact<PERSON>',
    aliases: ['ttt'],
    category: 'game',
    description: 'Play a game of Tic-Tac-Toe against another player or the bot.',
    usePrefix: true,
    isEverywhere: false,
    async execute(message) {
        const opponent = message.mentions.users.first() || message.client.user;
        const player = message.author;
        const board = Array(9).fill(null);
        const symbols = { [player.id]: '❌', [opponent.id]: '⭕' };
        let currentPlayer = player;
        let gameOver = false;

        function renderBoard() {
            return board.map(cell => cell || '🔳').reduce((rows, cell, i) => {
                if (i % 3 === 0) rows.push([]);
                rows[rows.length - 1].push(cell);
                return rows;
            }, []).map(row => row.join(' ')).join('\n');
        }

        function checkWin(symbol) {
            const winPatterns = [
                [0, 1, 2], [3, 4, 5], [6, 7, 8],
                [0, 3, 6], [1, 4, 7], [2, 5, 8],
                [0, 4, 8], [2, 4, 6]
            ];
            return winPatterns.some(pattern => pattern.every(index => board[index] === symbol));
        }

        async function botMove() {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Bot "thinking" delay
            let emptyIndexes = board.map((v, i) => v === null ? i : null).filter(v => v !== null);
            let move = emptyIndexes[Math.floor(Math.random() * emptyIndexes.length)];
            board[move] = '⭕';
        }

        function createBoardButtons() {
            let rows = [];
            for (let i = 0; i < 9; i += 3) {
                rows.push(
                    new ActionRowBuilder().addComponents(
                        ...board.slice(i, i + 3).map((cell, j) =>
                            new ButtonBuilder()
                                .setCustomId(`move_${i + j}`)
                                .setLabel(cell || '⬜')
                                .setStyle(cell === '❌' ? ButtonStyle.Danger : cell === '⭕' ? ButtonStyle.Primary : ButtonStyle.Secondary)
                        )
                    )
                );
            }
            return rows;
        }

        function getGameEmbed(status, color = 'Blue') {
            return new EmbedBuilder()
                .setTitle('🎮 Tic-Tac-Toe')
                .setDescription(`${renderBoard()}\n\n${status}`)
                .setColor(color)
                .setThumbnail(currentPlayer.displayAvatarURL({ dynamic: true }))
                .addFields(
                    { name: '❌ Player 1', value: `${player}`, inline: true },
                    { name: '⭕ Player 2', value: `${opponent}`, inline: true }
                );
        }

        const gameMessage = await message.channel.send({
            embeds: [getGameEmbed(`**${currentPlayer}, it's your turn!**`)],
            components: createBoardButtons()
        });

        const collector = gameMessage.createMessageComponentCollector({ time: 60000 });

        collector.on('collect', async i => {
            if (i.user.id !== currentPlayer.id) {
                return i.reply({ content: '⛔ It\'s not your turn!', flags: MessageFlags.Ephemeral });
            }

            let move = parseInt(i.customId.split('_')[1]);
            if (board[move] !== null) return;

            board[move] = symbols[currentPlayer.id];

            if (checkWin(symbols[currentPlayer.id])) {
                gameOver = true;
                return i.update({
                    embeds: [getGameEmbed(`🎉 ${currentPlayer} wins!`, 'Green')],
                    components: [] // ❌ REMOVE BUTTONS
                });
            }

            if (!board.includes(null)) {
                gameOver = true;
                return i.update({
                    embeds: [getGameEmbed(`🤝 It's a draw!`, 'Yellow')],
                    components: [] // ❌ REMOVE BUTTONS
                });
            }

            currentPlayer = currentPlayer.id === player.id ? opponent : player;

            if (opponent.id === message.client.user.id && currentPlayer === opponent) {
                await botMove();
                if (checkWin('⭕')) {
                    gameOver = true;
                    return i.update({
                        embeds: [getGameEmbed(`🤖 The bot wins!`, 'Red')],
                        components: [] // ❌ REMOVE BUTTONS
                    });
                }
                currentPlayer = player;
            }

            await i.update({
                embeds: [getGameEmbed(`**${currentPlayer}, it's your turn!**`)],
                components: createBoardButtons()
            });
        });

        collector.on('end', () => {
            if (!gameOver) {
                gameMessage.edit({
                    embeds: [getGameEmbed('⏳ Game timed out!', 'Gray')],
                    components: [] // ❌ REMOVE BUTTONS
                });
            }
        });
    }
};
