const fs = require('fs');
const path = require('path');

module.exports = {
  name: 'kamus',
  aliases: [],
  category: 'utility',
  description: '<PERSON>i definisi kata dari kamus.json',
  usePrefix: true,
  isEverywhere: false,

  async execute(message, args) {
    if (!args.length) {
      return message.channel.send('❗ Masukkan kata yang ingin dicari, contoh: `!kamus ahli`');
    }

    const query = args.join(' ').toLowerCase();
    const kamusPath = path.join(__dirname, '../../utils/cleaned.json');

    let kamusData;
    try {
      kamusData = JSON.parse(fs.readFileSync(kamusPath, 'utf-8'));
    } catch (error) {
      console.error('Gagal membaca atau mengurai cleaned.json:', error);
      return message.channel.send('⚠️ <PERSON><PERSON><PERSON>di kesalahan saat memuat data kamus.');
    }

    const hasil = kamusData.find(entry => 
      entry.kata.toLowerCase() === query || entry.kata_input?.toLowerCase() === query
    );

    if (!hasil) {
      return message.channel.send(`🔍 Kata "${query}" tidak ditemukan di kamus.`);
    }

    const lines = [];

    if (hasil.pelafalan) lines.push(`**Pelafalan:** ${hasil.pelafalan}`);
    if (hasil.jenis) lines.push(`**Jenis:** ${hasil.jenis}`);

    if (Array.isArray(hasil.definisi) && hasil.definisi.length > 0) {
      const definisiText = hasil.definisi.map(d => `**${d.no}.** ${d.arti}`).join('\n');
      lines.push(`\n__**Definisi:**__\n${definisiText}`);
    }

    if (Array.isArray(hasil.turunan) && hasil.turunan.length > 0) {
      const turunanText = hasil.turunan.map(t => `• ${t.bentuk}: ${t.arti}`).join('\n');
      lines.push(`\n__**Turunan:**__\n${turunanText}`);
    }

    const embed = {
      color: 0x0099ff,
      title: hasil.kata,
      url: hasil.pranala || undefined,
      description: lines.join('\n'),
      footer: { text: 'Sumber: KBBI (kbbi.web.id)' }
    };

    return message.channel.send({ embeds: [embed] });
  }
};
