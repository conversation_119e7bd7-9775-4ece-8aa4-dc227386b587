const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle, MessageFlags } = require('discord.js');

module.exports = {
    name: 'connectfour',
    aliases: ['c4'],
    category: 'game',
    description: 'Play Connect Four with a stop button',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const player1 = message.author;
        let player2 = message.mentions.users.first();

        if (!player2 || player2.id === message.client.user.id) {
            player2 = message.client.user;
        }

        const players = [player1, player2];
        let currentPlayer = 0;
        let gameOver = false;
        let board = Array(6).fill(null).map(() => Array(7).fill('⚫'));

        const createButtons = () => {
            if (gameOver) return [];
            
            const rows = [
                new ActionRowBuilder(),
                new ActionRowBuilder(),
                new ActionRowBuilder()
            ];

            // Column buttons
            for (let i = 0; i < 7; i++) {
                const button = new ButtonBuilder()
                    .setCustomId(`col_${i}`)
                    .setLabel((i + 1).toString())
                    .setStyle(ButtonStyle.Primary);

                if (i < 5) rows[0].addComponents(button);
                else rows[1].addComponents(button);
            }

            // Stop game button
            rows[2].addComponents(
                new ButtonBuilder()
                    .setCustomId('stop_game')
                    .setLabel('Stop Game')
                    .setStyle(ButtonStyle.Danger)
            );

            return rows.filter(row => row.components.length > 0);
        };

        const checkWin = (symbol) => {
            for (let r = 0; r < 6; r++) {
                for (let c = 0; c < 7; c++) {
                    if (c + 3 < 7 && board[r][c] === symbol && 
                       board[r][c+1] === symbol && 
                       board[r][c+2] === symbol && 
                       board[r][c+3] === symbol) return true;
                    if (r + 3 < 6 && board[r][c] === symbol && 
                       board[r+1][c] === symbol && 
                       board[r+2][c] === symbol && 
                       board[r+3][c] === symbol) return true;
                    if (r + 3 < 6 && c + 3 < 7 && 
                       board[r][c] === symbol && 
                       board[r+1][c+1] === symbol && 
                       board[r+2][c+2] === symbol && 
                       board[r+3][c+3] === symbol) return true;
                    if (r - 3 >= 0 && c + 3 < 7 && 
                       board[r][c] === symbol && 
                       board[r-1][c+1] === symbol && 
                       board[r-2][c+2] === symbol && 
                       board[r-3][c+3] === symbol) return true;
                }
            }
            return false;
        };

        const makeMove = (col) => {
            for (let row = 5; row >= 0; row--) {
                if (board[row][col] === '⚫') {
                    board[row][col] = currentPlayer === 0 ? '🔴' : '🟡';
                    return row;
                }
            }
            return -1;
        };

        const createEmbed = () => {
            return new EmbedBuilder()
                .setTitle('🔴 Connect Four 🟡')
                .setDescription(board.map(row => row.join('')).join('\n'))
                .setColor(currentPlayer === 0 ? 'Red' : 'Yellow')
                .setFooter({ text: gameOver ? 'Game Over' : `${players[currentPlayer].username}'s turn`, iconURL: players[currentPlayer].displayAvatarURL() });
        };

        const gameMessage = await message.channel.send({ 
            embeds: [createEmbed()], 
            components: createButtons() 
        });

        const collector = gameMessage.createMessageComponentCollector();

        collector.on('collect', async (interaction) => {
            // Handle stop button
            if (interaction.customId === 'stop_game') {
                if (!players.some(p => p.id === interaction.user.id)) {
                    return interaction.reply({ content: "Only players can stop the game!", flags: MessageFlags.Ephemeral });
                }
                gameOver = true;
                collector.stop();
                await interaction.reply(`⏹️ Game stopped by ${interaction.user.username}`);
                return;
            }

            // Handle regular moves
            if (interaction.user.id !== players[currentPlayer].id) {
                return interaction.reply({ content: "It's not your turn!", flags: MessageFlags.Ephemeral });
            }

            const col = parseInt(interaction.customId.split('_')[1]);
            const row = makeMove(col);

            if (row === -1) {
                return interaction.reply({ content: "Column full! Try another.", flags: MessageFlags.Ephemeral });
            }

            const currentSymbol = currentPlayer === 0 ? '🔴' : '🟡';
            if (checkWin(currentSymbol)) {
                gameOver = true;
                collector.stop();
                await interaction.update({ embeds: [createEmbed()], components: [] });
                return interaction.followUp(`🎉 ${players[currentPlayer].username} wins!`);
            }

            currentPlayer = (currentPlayer + 1) % 2;
            
            // Bot move logic
            if (players[currentPlayer].bot) {
                const availableCols = [];
                for (let c = 0; c < 7; c++) {
                    if (board[0][c] === '⚫') availableCols.push(c);
                }
                if (availableCols.length > 0) {
                    const botCol = availableCols[Math.floor(Math.random() * availableCols.length)];
                    makeMove(botCol);
                    
                    if (checkWin('🟡')) {
                        gameOver = true;
                        collector.stop();
                        await interaction.update({ embeds: [createEmbed()], components: [] });
                        return interaction.followUp(`🎉 ${players[1].username} wins!`);
                    }
                    currentPlayer = 0;
                }
            }

            await interaction.update({ embeds: [createEmbed()], components: createButtons() });
        });

        collector.on('end', async () => {
            gameOver = true;
            await gameMessage.edit({ components: [] });
        });
    }
};