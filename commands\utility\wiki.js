const axios = require('axios');
const { EmbedBuilder } = require('discord.js');

// Fungsi untuk capitalize setiap kata dalam query
const capitalizeQuery = (text) => {
    return text
        .toLowerCase()
        .split(' ')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
};

module.exports = {
    name: 'wikipedia',
    aliases: ['wiki'],
    category: 'utility',
    description: 'Mencari informasi dari Wikipedia dalam berbagai bahasa.',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        if (!args.length) {
            return message.channel.send('❌ Mohon berikan kata kunci untuk dicari di Wikipedia.');
        }

        // Cek apakah pengguna menyertakan kode bahasa (ex: "id:Jakarta")
        let lang = 'en'; // Default ke bahasa Inggris
        let query = args.join(' ');

        if (query.includes(':')) {
            const parts = query.split(':');
            if (parts.length === 2) {
                lang = parts[0].toLowerCase();
                query = parts[1];
            }
        }

        // Capitalize query agar lebih akurat
        query = capitalizeQuery(query);

        const url = `https://${lang}.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(query)}`;

        try {
            const { data } = await axios.get(url);

            if (data.type === 'disambiguation') {
                return message.channel.send(`🔍 **${data.title}** memiliki beberapa hasil. Cek di sini: [${data.content_urls.desktop.page}](${data.content_urls.desktop.page})`);
            }

            // Pastikan title tetap rapi jika description tidak ada
            const title = data.description ? `${data.title} • ${data.description}` : data.title;

            // Hitung panjang artikel berdasarkan jumlah karakter di extract
            const articleLength = data.extract ? data.extract.length : 0;

            // Footer yang lebih akurat
            const footerText = `Wikipedia ${lang.toUpperCase()} • Panjang artikel: ${articleLength} karakter`;

            const embed = new EmbedBuilder()
                .setColor('#0088cc')
                .setTitle(title)
                .setURL(data.content_urls.desktop.page)
                .setDescription(data.extract ? (data.extract.length > 4096 ? data.extract.substring(0, 4093) + '...' : data.extract) : '*Tidak ada ringkasan yang tersedia.*')
                .setThumbnail(data.thumbnail ? data.thumbnail.source : 'https://upload.wikimedia.org/wikipedia/commons/6/63/Wikipedia-logo.png')
                .setFooter({ 
                    text: footerText, 
                    iconURL: 'https://upload.wikimedia.org/wikipedia/commons/6/63/Wikipedia-logo.png' 
                });

            message.channel.send({ embeds: [embed] });
        } catch (error) {
            console.error(error);
            message.channel.send('❌ Tidak dapat menemukan hasil di Wikipedia. Coba gunakan bahasa lain atau periksa kata kunci.');
        }
    }
};
