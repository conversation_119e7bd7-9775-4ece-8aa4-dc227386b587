const { ActivityType, EmbedBuilder } = require('discord.js');
const axios = require('axios');
const { checkYouTubeStatus } = require("../../plugin/javascript/hololive");
const fs = require('fs');
const path = require('path');


const wordlistPath = path.join(__dirname, '../../utils/indo-wordlist.txt');
const sentWordsPath = path.join(__dirname, '../../utils/sent-word.txt');
module.exports = (client) => {
    console.log(`✅ Bot is online as ${client.user.tag} (ID: ${client.user.id})`);

    // Function to update bot presence dynamically
    const statuses = [
        { name: 'Type /play', type: ActivityType.Listening },
        { name: `${client.guilds.cache.size} servers`, type: ActivityType.Watching },
        { name: 'with discord.js', type: ActivityType.Playing }
    ];

    let i = 0;
    setInterval(() => {
        client.user.setPresence({
            activities: [statuses[i]],
            status: 'online'
        });
        i = (i + 1) % statuses.length;
    }, 15000); // Change every 15 seconds

    console.log('✅ Status rotation initialized');

    // Function to send waifu image
    const channelId = process.env.botMainChannel;
    async function postWaifuImage() {
        const channel = client.channels.cache.get(channelId);
        if (!channel) return console.log('⚠️ Channel not found!');

        try {
            const response = await axios.get('https://api.waifu.pics/sfw/waifu');
            const imageUrl = response.data.url;

            const embed = new EmbedBuilder()
                .setTitle('Waifu every 30 minutes!')
                .setImage(imageUrl)
                .setColor('#FFC0CB')
                .setFooter({ text: 'Powered by waifu.pics' });

            await channel.send({ embeds: [embed] });
            console.log('✅ Waifu image posted');
        } catch (error) {
            console.error('❌ Error fetching waifu image:', error);
        }
    }

    // Post every 30 minutes (1800000 ms)
    setInterval(postWaifuImage, 1800000);
    // async function postRandomWord() {
    //     const channel = client.channels.cache.get(channelId);
    //     if (!channel) return console.log('⚠️ Channel not found!');
    
    //     try {
    //         const wordlist = fs.readFileSync(wordlistPath, 'utf8').split('\n').map(w => w.trim()).filter(w => w);
    //         const sentWords = fs.existsSync(sentWordsPath) ? fs.readFileSync(sentWordsPath, 'utf8').split('\n').map(w => w.trim()).filter(w => w) : [];
    
    //         const availableWords = wordlist.filter(word => !sentWords.includes(word));
    //         if (availableWords.length === 0) {
    //             console.log('⚠️ Semua kata sudah terkirim. Tidak ada kata baru untuk dikirim.');
    //             return;
    //         }
    
    //         const randomWord = availableWords[Math.floor(Math.random() * availableWords.length)];
    
    //         await channel.send(`# ${randomWord} bangsat`);
    
    //         // Save the sent word
    //         fs.appendFileSync(sentWordsPath, `${randomWord}\n`);
    
    //         console.log(`✅ Word posted: ${randomWord}`);
    //     } catch (error) {
    //         console.error('❌ Error posting random word:', error);
    //     }
    // }
    
    // // Post random word every 10 minutes (600000 ms)
    // setInterval(postRandomWord, 600000);

    checkYouTubeStatus(client);
    setInterval(() => {
        checkYouTubeStatus(client);
      }, 1000 * 60 * 2);
};
