const axios = require("axios");
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ComponentType } = require("discord.js");
const { TZDateMini } = require("@date-fns/tz");
const { format } = require("date-fns");
const { enUS } = require("date-fns/locale");

module.exports = {
  name: "hololive",
  aliases: ["holo"],
  category: "anime",
  description: "Shows currently live or upcoming Hololive streams with pagination and filters",
  usePrefix: true,
  isEverywhere: false,

  async execute(message, args) {
    try {
      const branch = args[0]?.toLowerCase(); // e.g. jp, en, id
      const validBranches = ["jp", "en", "id"];
      const org = validBranches.includes(branch) ? "Hololive" : undefined;
      const langFilter = validBranches.includes(branch) ? branch : undefined;

      const HOLODEX_API_KEY = process.env.HOLODEX_API_KEY;
      const limit = 10;

      const fetchStreams = async (status) => {
        const res = await axios.get("https://holodex.net/api/v2/live", {
          headers: { "X-APIKEY": HOLODEX_API_KEY },
          params: {
            org,
            lang: langFilter,
            limit: 50,
            status,
          },
        });
        return res.data;
      };

      let liveStreams = await fetchStreams("live");
      let fallbackToUpcoming = false;

      if (!liveStreams || liveStreams.length === 0) {
        liveStreams = await fetchStreams("upcoming");
        fallbackToUpcoming = true;
      }

      if (!liveStreams.length) {
        return message.channel.send("❌ No Hololive streams found.");
      }

      const pages = [];
      for (let i = 0; i < liveStreams.length; i += limit) {
        pages.push(liveStreams.slice(i, i + limit));
      }

      let page = 0;

      const getStreamEmbed = (stream, index) => {
        const streamerName = stream.channel.english_name || stream.channel.name || "Hololive Member";
        const liveTime = format(new TZDateMini(stream.available_at, "Asia/Jakarta"), "dd MMM yyyy, HH:mm", {
          locale: enUS,
          in: { timeZone: "Asia/Jakarta" },
        });

        const embed = new EmbedBuilder()
          .setTitle(stream.title || "No Title")
          .setURL(`https://www.youtube.com/watch?v=${stream.id}`)
          .setAuthor({ name: streamerName, iconURL: stream.channel.photo })
          .setColor(fallbackToUpcoming ? 0xffa500 : 0x1db954)
          .setImage(`https://i.ytimg.com/vi/${stream.id}/maxresdefault.jpg`)
          .addFields(
            { name: "📅 Time (WIB)", value: liveTime, inline: true },
            { name: "👥 Viewers", value: stream.live_viewers?.toString() || "N/A", inline: true },
            { name: "🆔 Topic ID", value: stream.topic_id?.toString() || "N/A", inline: true }
          )
          .setFooter({ text: `Status: ${stream.status.toUpperCase()} • Page ${page + 1}/${pages.length} • Stream ${index + 1}/${liveStreams.length}` });

        return embed;
      };

      const sendStreamMessage = async () => {
        const stream = pages[page][0];
        const embed = getStreamEmbed(stream, page * limit);
        const row = new ActionRowBuilder().addComponents(
          new ButtonBuilder()
            .setCustomId("prev")
            .setLabel("⬅️ Previous")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(page === 0),
          new ButtonBuilder()
            .setCustomId("next")
            .setLabel("Next ➡️")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(page === pages.length - 1),
          new ButtonBuilder()
            .setLabel("🔴 Watch Stream")
            .setStyle(ButtonStyle.Link)
            .setURL(`https://www.youtube.com/watch?v=${stream.id}`),
          new ButtonBuilder()
            .setLabel("📺 Channel")
            .setStyle(ButtonStyle.Link)
            .setURL(`https://www.youtube.com/channel/${stream.channel.id}`)
        );

        const sent = await message.channel.send({ embeds: [embed], components: [row] });

        const collector = sent.createMessageComponentCollector({
          componentType: ComponentType.Button,
          time: 60_000,
        });

        collector.on("collect", async (interaction) => {
          if (interaction.user.id !== message.author.id) {
            return interaction.reply({ content: "Only the command author can interact.", ephemeral: true });
          }

          await interaction.deferUpdate();

          if (interaction.customId === "prev" && page > 0) page--;
          else if (interaction.customId === "next" && page < pages.length - 1) page++;

          const stream = pages[page][0];
          const newEmbed = getStreamEmbed(stream, page * limit);
          const newRow = ActionRowBuilder.from(row).setComponents(
            row.components[0].setDisabled(page === 0),
            row.components[1].setDisabled(page === pages.length - 1),
            row.components[2].setURL(`https://www.youtube.com/watch?v=${stream.id}`),
            row.components[3].setURL(`https://www.youtube.com/channel/${stream.channel.id}`)
          );

          await sent.edit({ embeds: [newEmbed], components: [newRow] });
        });

        collector.on("end", () => {
          sent.edit({ components: [] }).catch(() => {});
        });
      };

      await sendStreamMessage();
    } catch (err) {
      console.error("Hololive command error:", err);
      message.channel.send("⚠️ Failed to fetch Hololive data.");
    }
  },
};
