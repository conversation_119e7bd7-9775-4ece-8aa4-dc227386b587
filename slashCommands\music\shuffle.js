const { <PERSON>lash<PERSON><PERSON>mandBuilder, <PERSON>bed<PERSON>uilder, MessageFlags } = require('discord.js');
const { useQueue } = require('discord-player');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('shuffle')
        .setDescription('Shuffle the current queue'),
    
    async execute(interaction) {
        const queue = useQueue(interaction.guild.id);

        if (!queue || queue.tracks.size === 0) {
            return interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ No songs in queue to shuffle!')
                        .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        queue.tracks.shuffle(); // 🔥 Fix: Make sure `tracks` exists

        return interaction.reply({
            embeds: [
                new EmbedBuilder()
                    .setDescription('🔀 Shuffled the queue.')
                    .setColor('#00FF00')
            ]
        });
    }
};
