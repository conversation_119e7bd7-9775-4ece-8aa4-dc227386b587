const axios = require("axios");
const { Em<PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");

module.exports = {
  name: "manga",
  aliases: ["mangainfo"],
  category: "anime",
  description: "Cari informasi manga dari MyAnimeList.",
  usePrefix: true,
  isEverywhere: false,

  async execute(message, args) {
    const query = args.join(" ");
    if (!query) {
      return message.reply("❌ Masukkan judul manga yang ingin dicari. Contoh: `!manga One Piece`");
    }

    try {
      const response = await axios.get(`https://api.jikan.moe/v4/manga?q=${encodeURIComponent(query)}&limit=10`);
      const mangaList = response.data.data;

      if (!mangaList || mangaList.length === 0) {
        return message.reply(`❌ Manga dengan judul **"${query}"** tidak ditemukan.`);
      }

      let currentIndex = 0;

      const fetchMangaDetails = async (mangaId) => {
        const detailsRes = await axios.get(`https://api.jikan.moe/v4/manga/${mangaId}/full`);
        return detailsRes.data.data;
      };

      const truncateText = (text, maxLength) => text.length > maxLength ? text.slice(0, maxLength) + "..." : text;

      const createEmbed = async (index) => {
        const manga = await fetchMangaDetails(mangaList[index].mal_id);

        return new EmbedBuilder()
          .setColor("#ff6600")
          .setTitle(manga.title)
          .setURL(manga.url)
          .setDescription(truncateText(manga.synopsis || "No synopsis available.", 2000))
          .setThumbnail(manga.images.jpg.image_url)
          .addFields(
            { name: "📌 Japanese", value: manga.title_japanese || "None", inline: true },
            { name: "📌 Volumes", value: manga.volumes ? manga.volumes.toString() : "Unknown", inline: true },
            { name: "📌 Chapters", value: manga.chapters ? manga.chapters.toString() : "Unknown", inline: true },
            { name: "📌 Status", value: manga.status || "Unknown", inline: true },
            { name: "📌 Published", value: manga.published.string || "Unknown", inline: false },
            { name: "📌 Authors", value: manga.authors.length > 0 ? manga.authors.map(author => author.name).join(", ") : "None", inline: false },
            { name: "📌 Genres", value: manga.genres.length > 0 ? manga.genres.map(genre => genre.name).join(", ") : "None", inline: false }
          )
          .setFooter({ text: `Manga ${index + 1} dari ${mangaList.length}` });
      };

      const createButtonRow = (index) => {
        return new ActionRowBuilder().addComponents(
          new ButtonBuilder()
            .setCustomId("prev_manga")
            .setLabel("◀ Prev")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(index === 0),
          new ButtonBuilder()
            .setCustomId("next_manga")
            .setLabel("Next ▶")
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(index === mangaList.length - 1),
          new ButtonBuilder()
            .setLabel("View on MAL")
            .setStyle(ButtonStyle.Link)
            .setURL(mangaList[index].url)
        );
      };

      let embed = await createEmbed(currentIndex);
      let buttons = createButtonRow(currentIndex);

      const sentMessage = await message.channel.send({
        embeds: [embed],
        components: [buttons]
      });

      const collector = sentMessage.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: 60000
      });

      collector.on("collect", async (interaction) => {
        await interaction.deferUpdate();

        if (interaction.customId === "prev_manga") {
          currentIndex--;
        } else if (interaction.customId === "next_manga") {
          currentIndex++;
        }

        const newEmbed = await createEmbed(currentIndex);
        const newButtons = createButtonRow(currentIndex);

        await sentMessage.edit({
          embeds: [newEmbed],
          components: [newButtons]
        });
      });

      collector.on("end", () => {
        sentMessage.edit({ components: [] }).catch(() => {});
      });

    } catch (error) {
      console.error("Error fetching manga data:", error);
      return message.reply("❌ Terjadi kesalahan saat mengambil data manga. Silakan coba lagi nanti.");
    }
  }
};
