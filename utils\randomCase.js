/**
 * Randomly alternates the casing of each letter in a string.
 *
 * @param {string} str - The input string.
 * @returns {string} - The transformed string with random uppercase and lowercase letters.
 */
function randomCase(str) {
    return str
        .split("")
        .map((char) => (Math.random() > 0.5 ? char.toUpperCase() : char.toLowerCase()))
        .join("");
}

module.exports = { randomCase };
