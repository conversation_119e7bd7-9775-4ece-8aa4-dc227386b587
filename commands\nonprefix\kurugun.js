const { PermissionsBitField } = require('discord.js');

module.exports = {
    name: 'kuru<PERSON>',
    aliases: [''],
    category: 'nonprefix',
    description: 'Send an image using a webhook with your nickname and avatar.',
    usePrefix: false,
    isEverywhere: false,
    async execute(message, args) {
        const imageURL = 'https://cdn.discordapp.com/attachments/752708484444848138/1315096247593599016/Kurukuru.gif?ex=67e880d2&is=67e72f52&hm=51846227cb6514d407d24ce6ca6e2fffb0cbfbebc9e32ba948f9730ca438692f&'; // URL gambar

        let targetMember = message.member;
        

        // Cek izin membuat webhook
        if (!message.channel.permissionsFor(message.client.user).has(PermissionsBitField.Flags.ManageWebhooks)) {
            return message.reply('I need the "Manage Webhooks" permission to send images.');
        }

        try {
            await message.delete();
            // Buat webhook khusus user
            const webhook = await message.channel.createWebhook({
                name: targetMember.displayName, // Gunakan server nickname
                avatar: targetMember.displayAvatarURL({ dynamic: true })
            });

            // Kirim pesan sebagai webhook
            await webhook.send({
                content: imageURL,
                username: targetMember.displayName, // Gunakan server nickname
                avatarURL: targetMember.displayAvatarURL({ dynamic: true })
            });

            // Hapus webhook setelah 5 detik untuk menghindari spam
            setTimeout(async () => {
                await webhook.delete();
            }, 1000);

        } catch (error) {
            console.error('Error creating or sending webhook:', error);
            message.reply('Failed to send the image.');
        }
    }
};
