const { EmbedBuilder } = require('discord.js');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

// <PERSON>-<PERSON> shuffle algorithm
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

module.exports = {
    name: 'shuffle',
    description: 'Shuffle the songs in the queue (except currently playing)',
    aliases: ['sh', 'mix'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || serverQueue.songs.length === 0) {
            const emptyQueueEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Empty Queue')
                .setDescription('The music queue is empty! Add some songs first.')
                .setTimestamp();
            return message.reply({ embeds: [emptyQueueEmbed] });
        }

        // If only one song (currently playing), nothing to shuffle
        if (serverQueue.songs.length <= 1) {
            const onlyCurrentEmbed = new EmbedBuilder()
                .setColor('#FFA500')
                .setTitle('⚠️ Not Enough Songs')
                .setDescription('There need to be at least 2 songs in the queue to shuffle!')
                .setTimestamp();
            return message.reply({ embeds: [onlyCurrentEmbed] });
        }

        const currentSong = serverQueue.songs[0]; // Keep the currently playing song at position 0
        const songsToShuffle = serverQueue.songs.slice(1); // Get all songs except the first one
        const shuffledSongs = shuffleArray(songsToShuffle);

        // Reconstruct the queue with current song first, then shuffled songs
        serverQueue.songs = [currentSong, ...shuffledSongs];

        const shuffleEmbed = new EmbedBuilder()
            .setColor('#9932CC')
            .setTitle('🔀 Queue Shuffled')
            .setDescription(`Successfully shuffled **${shuffledSongs.length}** song${shuffledSongs.length !== 1 ? 's' : ''} in the queue!`)
            .addFields({
                name: 'Currently Playing',
                value: currentSong ?
                    `[${currentSong.title}](${currentSong.displayUrl || currentSong.url})` :
                    'None',
                inline: false
            })
            .setFooter({
                text: `Total songs in queue: ${serverQueue.songs.length}`
            })
            .setTimestamp();

        // Show next few songs after shuffle
        if (shuffledSongs.length > 0) {
            const nextSongs = shuffledSongs.slice(0, 3).map((song, index) =>
                `${index + 2}. [${song.title}](${song.displayUrl || song.url})`
            ).join('\n');

            shuffleEmbed.addFields({
                name: 'Next Up',
                value: nextSongs + (shuffledSongs.length > 3 ? '\n...' : ''),
                inline: false
            });
        }

        message.reply({ embeds: [shuffleEmbed] });
    },
};
