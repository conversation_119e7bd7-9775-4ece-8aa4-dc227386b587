const { EmbedBuilder, PermissionsBitField } = require('discord.js');
const { memoryMonitor } = require('../../utils/memoryMonitor');
const { dbCleanup } = require('../../database/manager');

module.exports = {
    name: 'memory',
    description: 'Display memory usage statistics and perform cleanup operations',
    aliases: ['mem', 'ram'],
    usage: 'memory [status|cleanup|gc]',
    category: 'utility',
    cooldown: 5,
    permissions: [PermissionsBitField.Flags.Administrator],

    async execute(message, args) {
        const subcommand = args[0]?.toLowerCase();

        switch (subcommand) {
            case 'status':
            case 'stats':
                await this.showMemoryStatus(message);
                break;
            
            case 'cleanup':
                await this.performCleanup(message);
                break;
            
            case 'gc':
                await this.forceGarbageCollection(message);
                break;
            
            default:
                await this.showMemoryStatus(message);
                break;
        }
    },

    async showMemoryStatus(message) {
        try {
            const embed = memoryMonitor.createMemoryEmbed();
            const stats = memoryMonitor.getMemoryStats();
            
            // Add additional system info
            embed.addFields(
                { 
                    name: 'Process Info', 
                    value: `PID: ${process.pid}\nNode.js: ${process.version}\nPlatform: ${process.platform}`, 
                    inline: true 
                },
                { 
                    name: 'Active Resources', 
                    value: `Timers: ${global.client?.activeTimers?.size || 0}\nIntervals: ${global.client?.activeIntervals?.size || 0}\nProcesses: ${global.client?.activeProcesses?.size || 0}`, 
                    inline: true 
                }
            );

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Error showing memory status:', error);
            await message.reply('❌ Failed to get memory status.');
        }
    },

    async performCleanup(message) {
        try {
            const statusMsg = await message.reply('🧹 Starting cleanup operations...');
            
            // Perform various cleanup operations
            const cleanupTasks = [
                { name: 'Database Cleanup', task: () => dbCleanup.runAllCleanups() },
                { name: 'Memory Optimization', task: () => memoryMonitor.optimizeMemory() },
                { name: 'Resource Cleanup', task: () => memoryMonitor.cleanupResources() }
            ];

            const results = [];
            for (const { name, task } of cleanupTasks) {
                try {
                    await task();
                    results.push(`✅ ${name}`);
                } catch (error) {
                    results.push(`❌ ${name}: ${error.message}`);
                }
            }

            // Force garbage collection if available
            if (global.gc) {
                try {
                    global.gc();
                    results.push('✅ Garbage Collection');
                } catch (error) {
                    results.push(`❌ Garbage Collection: ${error.message}`);
                }
            } else {
                results.push('⚠️ Garbage Collection: Not available (run with --expose-gc)');
            }

            const embed = new EmbedBuilder()
                .setColor('#00FF00')
                .setTitle('🧹 Cleanup Results')
                .setDescription(results.join('\n'))
                .setTimestamp();

            await statusMsg.edit({ content: '', embeds: [embed] });
        } catch (error) {
            console.error('Error performing cleanup:', error);
            await message.reply('❌ Failed to perform cleanup operations.');
        }
    },

    async forceGarbageCollection(message) {
        try {
            if (!global.gc) {
                return message.reply('❌ Garbage collection is not available. Start the bot with `--expose-gc` flag.');
            }

            const beforeStats = memoryMonitor.getMemoryStats();
            
            // Force garbage collection
            global.gc();
            
            // Wait a moment for GC to complete
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            const afterStats = memoryMonitor.getMemoryStats();
            
            const heapBefore = beforeStats.current.heapUsed;
            const heapAfter = afterStats.current.heapUsed;
            const freed = heapBefore - heapAfter;
            
            const embed = new EmbedBuilder()
                .setColor(freed > 0 ? '#00FF00' : '#FFA500')
                .setTitle('🗑️ Garbage Collection Results')
                .addFields(
                    { name: 'Before', value: beforeStats.formatted.heapUsed, inline: true },
                    { name: 'After', value: afterStats.formatted.heapUsed, inline: true },
                    { name: 'Freed', value: freed > 0 ? memoryMonitor.formatBytes(freed) : 'No memory freed', inline: true }
                )
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error('Error forcing garbage collection:', error);
            await message.reply('❌ Failed to force garbage collection.');
        }
    }
};
