const { SlashCommandBuilder, EmbedBuilder, MessageFlags } = require('discord.js');
const { useQueue } = require('discord-player');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('skip')
        .setDescription('Skip the current song'),
    
    async execute(interaction) {
        const queue = useQueue(interaction.guild.id);

        if (!queue || !queue.currentTrack) {
            return interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ There is no song playing!')
                        .setColor('#FF0000') // Red color
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        const currentTrack = queue.currentTrack;
        const success = queue.node.skip();

        if (success) {
            return interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setTitle("⏭️ Track Skipped")
                        .setDescription(`Skipped **[${currentTrack.title || "Unknown"}](${currentTrack.url || "#"})**`)
                        .setThumbnail(currentTrack.thumbnail || "https://i.imgur.com/2n1cGDb.png")
                        .setColor("#FFA500") // Orange color
                        .setTimestamp()
                ]
            });
        } else {
            return interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ Failed to skip the current song!')
                        .setColor('#FF0000') // Red color
                ],
                flags: MessageFlags.Ephemeral
            });
        }
    }
};
