const { EmbedBuilder } = require("discord.js");
const {customCommandDB} = require('../../database/manager')
module.exports = {
    name: 'listcommands',
    aliases: ['customcommands', 'cmdlist'],
    category: 'custom',
    description: 'Display the list of available custom commands.',
    usePrefix: true,
    isEverywhere: false,

    async execute(message) {
        const keys = await customCommandDB.all();
        const customCommands = keys
            .filter(k => k.id.startsWith("custom_"))
            .map(k => {
                const { response, usePrefix, isEverywhere } = k.value;
                return { 
                    name: k.id.replace("custom_", ""), 
                    usePrefix, 
                    isEverywhere 
                };
            });

        if (customCommands.length === 0) {
            return message.reply('❌ No custom commands available.');
        }

        // Separate commands by category
        const prefixCommands = customCommands
            .filter(cmd => cmd.usePrefix && !cmd.isEverywhere)
            .map(cmd => `\`!${cmd.name}\``)
            .join(", ") || "None.";

        const noPrefixCommands = customCommands
            .filter(cmd => !cmd.usePrefix && !cmd.isEverywhere)
            .map(cmd => `\`${cmd.name}\``)
            .join(", ") || "None.";

        const everywhereCommands = customCommands
            .filter(cmd => cmd.isEverywhere)
            .map(cmd => `\`${cmd.name}\``)
            .join(", ") || "None.";

        // Create embed with separate fields
        const embed = new EmbedBuilder()
            .setColor("#ffcc00")
            .setTitle("📜 Custom Command List")
            .addFields(
                { name: "📌 With Prefix (!)", value: prefixCommands, inline: false },
                { name: "📝 Without Prefix", value: noPrefixCommands, inline: false },
                { name: "🌍 Everywhere", value: everywhereCommands, inline: false }
            )
            .setFooter({ 
                text: `Requested by ${message.author.username}`, 
                iconURL: message.author.displayAvatarURL() 
            });

        return message.reply({ embeds: [embed] });
    }
};
