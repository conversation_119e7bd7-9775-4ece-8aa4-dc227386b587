const {
    ContextMenuCommandBuilder,
    ApplicationCommandType,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    MessageFlags,
} = require('discord.js');

const { snipeDB } = require('../../database/manager')

module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('Snipe User')
        .setType(ApplicationCommandType.User),

    async execute(client, interaction) {
        const target = interaction.targetUser;
        const key = `snipe_${interaction.guildId}_${target.id}`;
        const logs = await snipeDB.get(key);

        if (!logs || logs.length === 0) {
            return interaction.reply({
                content: '❌ No deleted messages found for this user.',
                flags: MessageFlags.Ephemeral,
            });
        }

        const sessionId = `snipeuser_${interaction.id}`;
        const index = 0;

        await snipeDB.set(sessionId, {
            logs,
            index,
            userId: target.id,
            guildId: interaction.guildId,
        });

        const embed = buildSnipeEmbed(logs[index], index, logs.length);
        const row = buildPaginationRow(index, logs.length, sessionId);

        return interaction.reply({
            content: `🧹 Showing deleted messages from ${target.tag}`,
            embeds: [embed],
            components: [row],
            flags: MessageFlags.Ephemeral,
        });
    },
};

function buildSnipeEmbed(log, index, total) {
    const embed = new EmbedBuilder()
        .setColor('#FF7F7F')
        .setAuthor({ name: log.authorTag })
        .setTitle(`🕵️ Deleted Message #${index + 1}`)
        .setFooter({ text: `Deleted at • ${new Date(log.deletedAt).toLocaleString()} | ${index + 1} / ${total}` });

    if (log.content) {
        embed.setDescription(log.content.length > 2048 ? log.content.slice(0, 2045) + '...' : log.content);
    } else {
        embed.setDescription('*[No text content]*');
    }

    if (log.attachments?.length > 0) {
        embed.addFields({
            name: '📎 Attachment(s)',
            value: log.attachments.map((url, idx) => `[Attachment ${idx + 1}](${url})`).join('\n'),
        });

        const imageAttachment = log.attachments.find(url => {
            const baseUrl = url.split('?')[0];
            return /\.(jpeg|jpg|gif|png|webp)$/i.test(baseUrl);
        });
        
        if (imageAttachment) {
            embed.setImage(imageAttachment);
        }
    }

    if (log.embeds?.length > 0) {
        const embedDetails = log.embeds.map((e, idx) => {
            return `**Embed ${idx + 1}**\n${e.title ? `• Title: ${e.title}\n` : ''}${e.description ? `• Desc: ${e.description.slice(0, 100)}\n` : ''}${e.url ? `• URL: ${e.url}` : ''}`;
        }).join('\n\n');
        embed.addFields({ name: '🖼️ Embedded Content', value: embedDetails.slice(0, 1024) });
    }

    return embed;
}

function buildPaginationRow(index, total, sessionId) {
    return new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId(`snipeuser=prev:${sessionId}`)
            .setLabel('◀️ Previous')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(index === 0),
        new ButtonBuilder()
            .setCustomId(`snipeuser=next:${sessionId}`)
            .setLabel('Next ▶️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(index >= total - 1),
    );
}