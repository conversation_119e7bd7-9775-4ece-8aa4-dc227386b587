const { EmbedBuilder } = require('discord.js');

module.exports = {
    name: 'pilih',
    aliases: ['choose'],
    category: 'fun',
    description: 'Males milih? Biar bot yang pilih!\nExample: `!pilih touchgrass;touchbed`',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const optionString = args.join(" ");
        if (!optionString.includes(';')) {
            return message.reply('Hæh? Gunakan format yang benar: `!pilih opsi1;opsi2`');
        }

        const options = optionString.split(";").map(opt => opt.trim()).filter(opt => opt);
        if (options.length < 2) {
            return message.reply('<PERSON><PERSON> man, pake commandnya yang bener. Minimal 2 pilihan dipisah dengan `;`.');
        }

        const chosen = options[Math.floor(Math.random() * options.length)];

        const embed = new EmbedBuilder()
            .setColor('#5865F2')
            .setTitle('🤔 Aku pilih...')
            .setDescription(`**${chosen}**`)
            .setFooter({ text: `Diminta oleh ${message.author.username}`, iconURL: message.author.displayAvatarURL() });

        message.reply({ embeds: [embed] });
    }
};
