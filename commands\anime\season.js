const axios = require("axios");
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");

module.exports = {
    name: "season",
    aliases: ["se"],
    category: "anime",
    description: "Cari anime berdasarkan season dan tahun dari Jikan API.",
    usePrefix: true,
    isEverywhere: false,

    async execute(message, args) {
        // Expecting command usage like: !season 2025 spring
        if (args.length < 2) {
            return message.reply("❌ Masukkan tahun dan season. Contoh: `!season 2025 spring`");
        }

        const year = args[0];
        const season = args[1].toLowerCase();
        const validSeasons = ['winter', 'spring', 'summer', 'fall'];
        if (!validSeasons.includes(season)) {
            return message.reply("❌ Season tidak valid. Pilih salah satu: winter, spring, summer, fall.");
        }

        let currentPage = 1;
        let currentIndex = 0;
        let animeList = [];
        let pagination = {};

        // Function to fetch season data for a given page
        const fetchSeasonData = async (page) => {
            const res = await axios.get(`https://api.jikan.moe/v4/seasons/${year}/${season}?page=${page}`);
            return { animeList: res.data.data, pagination: res.data.pagination };
        };

        try {
            // Fetch the initial page
            const seasonData = await fetchSeasonData(currentPage);
            animeList = seasonData.animeList;
            pagination = seasonData.pagination;

            if (!animeList || animeList.length === 0) {
                return message.reply(`❌ Tidak ada anime ditemukan untuk season **${season} ${year}** pada halaman ${currentPage}.`);
            }

            // Helper: Fetch full details for an anime by its MAL ID
            const fetchAnimeDetails = async (animeId) => {
                const res = await axios.get(`https://api.jikan.moe/v4/anime/${animeId}/full`);
                return res.data.data;
            };

            // Helper: Create an embed with detailed anime info
            const createEmbed = async (index) => {
                const anime = await fetchAnimeDetails(animeList[index].mal_id);
                return new EmbedBuilder()
                    .setColor("#0099ff")
                    .setTitle(anime.title)
                    .setURL(`https://myanimelist.net/anime/${anime.mal_id}`)
                    .setDescription(
                        anime.synopsis
                            ? (anime.synopsis.length > 2000 ? anime.synopsis.slice(0, 2000) + "..." : anime.synopsis)
                            : "No synopsis available."
                    )
                    .setThumbnail(anime.images.jpg.large_image_url)
                    .addFields(
                        { name: "📌 Japanese", value: anime.title_japanese || "Unknown", inline: true },
                        { name: "📌 Synonyms", value: anime.title_synonyms.length > 0 ? anime.title_synonyms.join(", ") : "None", inline: true },
                        { name: "📌 Type", value: anime.type || "Unknown", inline: true },
                        { name: "📌 Episodes", value: anime.episodes ? anime.episodes.toString() : "Unknown", inline: true },
                        { name: "📌 Duration", value: anime.duration || "Unknown", inline: true },
                        { name: "📌 Status", value: anime.status || "Unknown", inline: true },
                        { name: "📌 Score", value: anime.score ? anime.score.toString() : "N/A", inline: true },
                        { name: "📌 Rating", value: anime.rating || "Unrated", inline: true },
                        { name: "📌 Genre", value: anime.genres.length > 0 ? anime.genres.map(g => g.name).join(", ") : "Unknown", inline: false },
                        { name: "📌 Studios", value: anime.studios.length > 0 ? anime.studios.map(s => s.name).join(", ") : "Unknown", inline: false },
                        { name: "📌 Aired", value: anime.aired.string || "Unknown", inline: false },
                        { name: "📌 Source", value: anime.source || "Unknown", inline: true }
                    )
                    .setFooter({ text: `Anime ${index + 1} dari ${animeList.length} - Halaman ${currentPage} (${season} ${year})` });
            };

            // Helper: Create a row of buttons for navigating anime and pages
            const createButtonRow = (index) => {
                return new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId("prev_anime")
                        .setLabel("◀ Anime")
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(index === 0),
                    new ButtonBuilder()
                        .setCustomId("next_anime")
                        .setLabel("Anime ▶")
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(index === animeList.length - 1),
                    new ButtonBuilder()
                        .setCustomId("prev_page")
                        .setLabel("◀ Page")
                        .setStyle(ButtonStyle.Primary)
                        .setDisabled(currentPage === 1),
                    new ButtonBuilder()
                        .setCustomId("next_page")
                        .setLabel("Page ▶")
                        .setStyle(ButtonStyle.Primary)
                        .setDisabled(!pagination.has_next_page),
                    new ButtonBuilder()
                        .setLabel("View on MAL")
                        .setStyle(ButtonStyle.Link)
                        .setURL(`https://myanimelist.net/anime/${animeList[index].mal_id}`)
                );
            };

            // Create and send the initial embed and buttons
            let embed = await createEmbed(currentIndex);
            let buttons = createButtonRow(currentIndex);

            const sentMessage = await message.channel.send({
                embeds: [embed],
                components: [buttons]
            });

            const collector = sentMessage.createMessageComponentCollector({
                filter: i => i.user.id === message.author.id,
                time: 60000
            });

            collector.on("collect", async (interaction) => {
                await interaction.deferUpdate();
                let updateSeasonData = false;

                if (interaction.customId === "prev_anime") {
                    currentIndex--;
                } else if (interaction.customId === "next_anime") {
                    currentIndex++;
                } else if (interaction.customId === "prev_page") {
                    if (currentPage > 1) {
                        currentPage--;
                        updateSeasonData = true;
                    }
                } else if (interaction.customId === "next_page") {
                    if (pagination.has_next_page) {
                        currentPage++;
                        updateSeasonData = true;
                    }
                }

                if (updateSeasonData) {
                    try {
                        const seasonData = await fetchSeasonData(currentPage);
                        animeList = seasonData.animeList;
                        pagination = seasonData.pagination;
                        currentIndex = 0;
                    } catch (error) {
                        console.error("Error fetching new season page:", error);
                        return interaction.followUp({ content: "❌ Terjadi kesalahan saat mengambil data halaman baru.", ephemeral: true });
                    }
                }

                // Make sure currentIndex remains within bounds after any update
                if (currentIndex < 0) currentIndex = 0;
                if (currentIndex >= animeList.length) currentIndex = animeList.length - 1;

                const updatedEmbed = await createEmbed(currentIndex);
                const updatedButtons = createButtonRow(currentIndex);

                await sentMessage.edit({
                    embeds: [updatedEmbed],
                    components: [updatedButtons]
                });
            });

            collector.on("end", () => {
                sentMessage.edit({ components: [] }).catch(() => {});
            });

        } catch (error) {
            console.error("Error fetching season data:", error);
            return message.reply("❌ Terjadi kesalahan saat mengambil data season. Silakan coba lagi nanti.");
        }
    }
};
