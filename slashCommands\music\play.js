const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>bed<PERSON><PERSON><PERSON>, PermissionsBitField, MessageFlags } = require('discord.js');
const { useMainPlayer } = require('discord-player');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('play')
        .setDescription('Play a song or playlist from any supported source using custom yt-dlp extractor')
        .addStringOption(option =>
            option.setName('query')
                .setDescription('Song name, YouTube URL, or direct URL from supported sites')
                .setRequired(true)
                .setAutocomplete(true)
        ),

    async autocompleteRun(interaction) {
        const query = interaction.options.getString('query', true);

        if (!query || query.length < 2) {
            return interaction.respond([]);
        }

        try {
            // Use our custom extractor's YouTube search for autocomplete
            const { searchYouTube } = require('../../extractors/ytdlpExtractorUtils');
            const results = await searchYouTube(query, 5); // Get 5 suggestions

            if (!results || results.length === 0) {
                return interaction.respond([]);
            }

            const suggestions = results.map(result => ({
                name: `${result.title} - ${result.author}`.substring(0, 100),
                value: result.url
            }));

            return interaction.respond(suggestions);
        } catch (error) {
            console.error('Autocomplete error:', error);
            return interaction.respond([]);
        }
    },

    async execute(interaction) {
        const player = useMainPlayer();
        await interaction.deferReply();

        const voiceChannel = interaction.member.voice.channel;
        if (!voiceChannel) {
            return interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ **You need to be in a voice channel!**')
                        .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        const botPermissions = voiceChannel.permissionsFor(interaction.guild.members.me);
        if (!botPermissions.has(PermissionsBitField.Flags.Connect)) {
            return interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ **I need `CONNECT` permissions to join the channel!**')
                        .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }
        if (!botPermissions.has(PermissionsBitField.Flags.Speak)) {
            return interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ **I need `SPEAK` permissions to play music!**')
                        .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        const query = interaction.options.getString('query');

        // Force our custom extractor to be used by specifying searchEngine
        const searchResult = await player.search(query, {
            requestedBy: interaction.user,
            searchEngine: 'ytdlp-extractor'
        });

        if (!searchResult.hasTracks()) {
            return interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription(`❌ **No results found for \`${query}\`!**`)
                        .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        try {
            if (searchResult.playlist) {
                await player.play(voiceChannel, searchResult.playlist, {
                    nodeOptions: {
                        metadata: interaction.channel,
                        volume: 100,
                      


                    }
                });

                return interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('📜 Playlist Added to Queue')
                            .setDescription(`**[${searchResult.playlist.title}](${searchResult.playlist.url})**`)
                            .setThumbnail(searchResult.playlist.thumbnail || interaction.client.user.displayAvatarURL())
                            .setColor('#00FF00')
                            .addFields(
                                { name: '🎵 Songs', value: `${searchResult.tracks.length}`, inline: true },
                                { name: '👤 Requested By', value: `<@${interaction.user.id}>`, inline: true }
                            )
                            .setFooter({ text: `Source: ${searchResult.playlist.source}` })
                    ]
                });
            } else {
                const { track } = await player.play(voiceChannel, searchResult.tracks[0].url, {
                    nodeOptions: {
                        metadata: interaction.channel,
                        volume: 100,
                        
                    }
                });

                return interaction.editReply({
                    embeds: [
                        new EmbedBuilder()
                            .setTitle('🎵 Added to Queue')
                            .setDescription(`**[${track.title}](${track.url})**`)
                            .setThumbnail(track.thumbnail || interaction.client.user.displayAvatarURL())
                            .setColor('#00FF00')
                            .addFields(
                                { name: '⏳ Duration', value: track.duration, inline: true },
                                { name: '🎤 Artist', value: track.author || 'Unknown', inline: true },
                                { name: '👤 Requested By', value: `<@${interaction.user.id}>`, inline: true }
                            )
                            .setFooter({ text: `Source: ${track.source}` })
                    ]
                });
            }
        } catch (error) {
            console.error(error);
            return interaction.editReply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ **Failed to play the song.**')
                        .setColor('#FF0000')
                ],
                flags: MessageFlags.Ephemeral
            });
        }
    }
};
