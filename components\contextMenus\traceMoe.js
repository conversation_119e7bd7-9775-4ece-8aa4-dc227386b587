const { ContextMenuCommandBuilder, ApplicationCommandType, EmbedBuilder, MessageFlags, ButtonBuilder, ActionRowBuilder, ButtonStyle } = require('discord.js');
const axios = require('axios');

// Store user searches with interaction ID as key
const userSearches = new Map();

module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('Identify Anime Scene')
        .setType(ApplicationCommandType.Message),

        async execute(client, interaction) {
            await interaction.deferReply({ flags: MessageFlags.Ephemeral });
        
            const message = interaction.targetMessage;
            let imageUrl;
        
            // 1. Try image from attachment
            const attachment = message.attachments.first();
            if (attachment && attachment.contentType?.startsWith("image/")) {
                imageUrl = attachment.url;
            }
        
            // 2. Try image URL from message content
            if (!imageUrl) {
                const urlRegex = /(https?:\/\/.*\.(?:png|jpg|jpeg|gif|webp))/i;
                const match = message.content.match(urlRegex);
                if (match) imageUrl = match[1];
            }
        
            // 3. Try embed image or thumbnail
            if (!imageUrl && message.embeds.length > 0) {
                const embed = message.embeds[0];
                imageUrl = embed.image?.url || embed.thumbnail?.url;
            }
        
            if (!imageUrl) {
                return interaction.editReply({
                    content: "❌ No valid image found! Try selecting a message with an uploaded image or image link.",
                });
            }
        
            try {
                const traceMoeURL = `https://api.trace.moe/search?anilistInfo&cutBorders&url=${encodeURIComponent(imageUrl)}`;
                const response = await axios.get(traceMoeURL);
        
                if (!response.data.result.length) {
                    return interaction.editReply({ content: "❌ No anime match found!" });
                }
        
                const results = response.data.result;
                userSearches.set(interaction.id, { results, index: 0 });
        
                const initialResult = results[0];
                const embed = this.createEmbed(initialResult, interaction.user);
                const buttons = this.createButtons(interaction.id, 0, results.length);
                const row = new ActionRowBuilder().addComponents(buttons);
        
                const reply = await interaction.editReply({ 
                    embeds: [embed], 
                    components: [row], 
                    fetchReply: true 
                });
        
                const filter = (i) => i.user.id === interaction.user.id;
                const collector = reply.createMessageComponentCollector({ filter, time: 5 * 60 * 1000 });
        
                collector.on('collect', async (i) => {
                    await this.handleButtonInteraction(i, interaction.id, reply);
                });
        
                collector.on('end', () => {
                    userSearches.delete(interaction.id);
                    reply.edit({ components: [] }).catch(err => {
                        if (err.code !== 10008) console.error(err);
                    });
                });
        
            } catch (error) {
                console.error("Error fetching TraceMoe data:", error);
                return interaction.editReply({
                    content: `⚠️ Error fetching anime details: ${error.response?.status || "Unknown error"}`,
                });
            }
        },
        
    createEmbed(result, user) {
        const anilist = result.anilist || {};
        const animeTitle = anilist.title?.native || "Unknown";
        const romajiTitle = anilist.title?.romaji || "Unknown";
        const englishTitle = anilist.title?.english || "None";
        const synonyms = anilist.synonyms?.length ? anilist.synonyms.join(", ") : "None";
        const episode = result.episode ?? "Unknown"; 
        const similarity = result.similarity ? (result.similarity * 100).toFixed(2) : "N/A";
        const previewURL = result.image || null;
        const timestamp = result.from ? new Date(result.from * 1000).toISOString().substr(11, 8) : "Unknown";
        
        const anilistLink = anilist.id ? `https://anilist.co/anime/${anilist.id}` : "N/A";
        const malLink = anilist.idMal ? `https://myanimelist.net/anime/${anilist.idMal}` : "N/A";

        const embed = new EmbedBuilder()
            .setTitle("🔍 Anime Scene Identified")
            .setColor("#FFB6C1")
            .addFields(
                { name: "Title", value: animeTitle, inline: true },
                { name: "Romaji", value: romajiTitle, inline: true },
                { name: "English", value: englishTitle, inline: true },
                { name: "Synonyms", value: synonyms, inline: false },
                { name: "Episode", value: `${episode}`, inline: true },
                { name: "Timestamp", value: timestamp, inline: true },
                { name: "Similarity", value: `${similarity}%`, inline: true },
                { name: "Anilist", value: anilistLink, inline: false },
                { name: "MyAnimeList", value: malLink, inline: false }
            )
            .setFooter({ text: "Powered by TraceMoe", iconURL: user.displayAvatarURL() });

        if (previewURL) {
            embed.setThumbnail(previewURL).setImage(previewURL);
        }

        return embed;
    },

    createButtons(interactionId, currentIndex, totalResults) {
        return [
            new ButtonBuilder()
                .setCustomId(`page_prev:${interactionId}`)
                .setLabel('Previous')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentIndex === 0),
            new ButtonBuilder()
                .setCustomId(`page_next:${interactionId}`)
                .setLabel('Next')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentIndex === totalResults - 1)
        ];
    },

    async handleButtonInteraction(interaction, originalInteractionId, originalReply) {
        await interaction.deferUpdate();
        const data = userSearches.get(originalInteractionId);

        if (!data) {
            return interaction.editReply({ content: '❌ This search session has expired!', components: [] });
        }

        // Determine action and update index
        const action = interaction.customId.startsWith('page_prev') ? 'prev' : 'next';
        let newIndex = data.index + (action === 'prev' ? -1 : 1);

        // Validate new index
        if (newIndex < 0 || newIndex >= data.results.length) {
            return;
        }

        // Update stored data
        data.index = newIndex;
        userSearches.set(originalInteractionId, data);

        // Create new embed and buttons
        const newResult = data.results[newIndex];
        const newEmbed = this.createEmbed(newResult, interaction.user);
        const buttons = this.createButtons(originalInteractionId, newIndex, data.results.length);
        const row = new ActionRowBuilder().addComponents(buttons);

        // Update the message
        try {
            await interaction.editReply({ 
                embeds: [newEmbed], 
                components: [row] 
            });
        } catch (err) {
            if (err.code !== 10008) console.error("Failed to update message:", err);
        }
    }
};
