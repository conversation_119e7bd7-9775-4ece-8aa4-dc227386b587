const { EmbedBuilder } = require('discord.js');

module.exports = (player, queue, error) => {
    console.error(`❌ Player error: ${error.stack || error.message}`);

    try {
        if (!queue.metadata || !queue.metadata.send) return;

        // Extract only the first relevant part of the error message
        let errorMessage = error.message.split("\n")[0]; // Take only the first line

        // Limit length to avoid sending excessive error details
        if (errorMessage.length > 100) {
            errorMessage = errorMessage.substring(0, 97) + "..."; // Truncate long messages
        }

        const embed = new EmbedBuilder()
            .setTitle("❌ Error")
            .setDescription(`An error occurred while playing music:\n\`\`\`${errorMessage}\`\`\``)
            .setColor("#FF0000")
            .setTimestamp();

        queue.metadata.send({ embeds: [embed] }).catch(console.error);
    } catch (err) {
        console.error("Error sending player error embed:", err);
    }
};
