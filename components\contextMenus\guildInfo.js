const { ContextMenuCommandBuilder, ApplicationCommandType, EmbedBuilder, MessageFlags, GuildVerificationLevel, GuildNSFWLevel } = require('discord.js');

module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('Guild Info')
        .setType(ApplicationCommandType.Message),

    async execute(client, interaction) {
        const guild = interaction.guild;
        const owner = await guild.fetchOwner();
        const emojis = guild.emojis.cache;
        const stickers = guild.stickers.cache;
        const createdTimestamp = Math.floor(guild.createdTimestamp / 1000);
        const roles = guild.roles.cache.sort((a, b) => b.position - a.position).map(r => r.name).join(", ");
        const features = guild.features.length ? guild.features.map(f => `\`${f.replace(/_/g, " ")}\``).join(", ") : "None";
        
        const members = await guild.members.fetch();
        const onlineMembers = members.filter(m => m.presence?.status === 'online').size;
        const offlineMembers = members.filter(m => !m.presence || m.presence.status === 'offline').size;
        const botCount = members.filter(m => m.user.bot).size;
        const humanCount = guild.memberCount - botCount;

        const textChannels = guild.channels.cache.filter(c => c.type === 0).size;
        const voiceChannels = guild.channels.cache.filter(c => c.type === 2).size;
        const categoryChannels = guild.channels.cache.filter(c => c.type === 4).size;
        const stageChannels = guild.channels.cache.filter(c => c.type === 13).size;
        const forumChannels = guild.channels.cache.filter(c => c.type === 15).size;
        const announcementChannels = guild.channels.cache.filter(c => c.type === 5).size;

        const bannerUrl = guild.bannerURL({ dynamic: true, size: 1024 });
        const splashUrl = guild.splashURL({ dynamic: true, size: 1024 });
        const discoverySplashUrl = guild.discoverySplashURL({ dynamic: true, size: 1024 });

        const embed = new EmbedBuilder()
            .setColor(0x5865F2)
            .setTitle(`🏠 Guild Info: ${guild.name}`)
            .setThumbnail(guild.iconURL({ dynamic: true, size: 1024 }) || null)
            .addFields(
                { name: "📛 Name", value: guild.name, inline: true },
                { name: "🆔 ID", value: guild.id, inline: true },
                { name: "👑 Owner", value: `<@${owner.id}>`, inline: false },
                { name: "📅 Created", value: `<t:${createdTimestamp}:F> (<t:${createdTimestamp}:R>)`, inline: false },
                { name: "🚀 Boost Level", value: `Level ${guild.premiumTier}`, inline: true },
                { name: "💎 Boosters", value: `${guild.premiumSubscriptionCount || 0}`, inline: true },
                { name: "🛡️ Verification Level", value: GuildVerificationLevel[guild.verificationLevel], inline: true },
                { name: "🔞 NSFW Level", value: GuildNSFWLevel[guild.nsfwLevel], inline: true },
                { name: "🔐 MFA Requirement", value: guild.mfaLevel ? "Enabled" : "Disabled", inline: true },
                { name: "👥 Members", value: `Total: **${guild.memberCount}**\n👤 Humans: **${humanCount}**\n🤖 Bots: **${botCount}**`, inline: true },
                { name: "💠 Online/Offline", value: `🟢 Online: **${onlineMembers}**\n⚫ Offline: **${offlineMembers}**`, inline: true },
                { name: "📜 Roles", value: `Total: **${guild.roles.cache.size}**\n${roles.length > 1024 ? roles.slice(0, 1020) + '...' : roles}`, inline: false },
                { name: "📂 Channels", value: `📝 Text: **${textChannels}**\n🔊 Voice: **${voiceChannels}**\n🎤 Stage: **${stageChannels}**\n📂 Categories: **${categoryChannels}**\n📢 Announcement: **${announcementChannels}**\n📌 Forum: **${forumChannels}**`, inline: false },
                { name: "✨ Features", value: features, inline: false },
                { name: "🔕 Default Notifications", value: guild.defaultMessageNotifications === 0 ? "All Messages" : "Only @mentions", inline: true },
                { name: "📡 Widget Enabled?", value: guild.widgetEnabled ? "Yes" : "No", inline: true },
                { name: "📌 Rules Channel", value: guild.rulesChannel ? `<#${guild.rulesChannel.id}>` : "None", inline: true },
                { name: "📢 Community Updates", value: guild.publicUpdatesChannel ? `<#${guild.publicUpdatesChannel.id}>` : "None", inline: true },
                { name: "🌎 Preferred Locale", value: guild.preferredLocale, inline: true },
                { name: "📍 Region", value: guild.region || "Auto (Deprecated)", inline: true }
            )
            .setFooter({ text: `Requested by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) })
            .setTimestamp();

        if (bannerUrl) embed.setImage(bannerUrl);
        else if (splashUrl) embed.setImage(splashUrl);
        else if (discoverySplashUrl) embed.setImage(discoverySplashUrl);

        await interaction.reply({ embeds: [embed], flags: MessageFlags.Ephemeral });
    }
};
