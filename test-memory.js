// Simple test script to verify memory monitoring functionality
const { memoryMonitor } = require('./utils/memoryMonitor');

console.log('🧪 Testing Memory Monitor...');

// Start monitoring
memoryMonitor.startMonitoring(5000); // Check every 5 seconds for testing

// Get initial stats
const initialStats = memoryMonitor.getMemoryStats();
console.log('📊 Initial Memory Stats:');
console.log(`  Heap Used: ${initialStats.formatted.heapUsed}`);
console.log(`  RSS: ${initialStats.formatted.rss}`);
console.log(`  Monitoring: ${initialStats.isMonitoring ? '✅' : '❌'}`);

// Create some memory pressure for testing
console.log('🔄 Creating memory pressure...');
const testData = [];
for (let i = 0; i < 1000; i++) {
    testData.push(new Array(1000).fill('test data'));
}

// Wait a moment
setTimeout(() => {
    const afterStats = memoryMonitor.getMemoryStats();
    console.log('📊 After Memory Pressure:');
    console.log(`  Heap Used: ${afterStats.formatted.heapUsed}`);
    console.log(`  RSS: ${afterStats.formatted.rss}`);
    
    // Test cleanup
    console.log('🧹 Testing cleanup...');
    memoryMonitor.optimizeMemory();
    
    // Force garbage collection if available
    if (global.gc) {
        console.log('🗑️ Running garbage collection...');
        global.gc();
    } else {
        console.log('⚠️ Garbage collection not available (run with --expose-gc)');
    }
    
    setTimeout(() => {
        const finalStats = memoryMonitor.getMemoryStats();
        console.log('📊 Final Memory Stats:');
        console.log(`  Heap Used: ${finalStats.formatted.heapUsed}`);
        console.log(`  RSS: ${finalStats.formatted.rss}`);
        
        // Stop monitoring
        memoryMonitor.stopMonitoring();
        console.log('✅ Memory monitor test completed');
        
        process.exit(0);
    }, 2000);
}, 3000);
