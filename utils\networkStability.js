const { EmbedBuilder } = require('discord.js');

/**
 * Network stability monitoring and diagnostics
 */
class NetworkStabilityMonitor {
    constructor() {
        this.guildStats = new Map();
        this.globalStats = {
            totalDisconnections: 0,
            totalReconnections: 0,
            averageLatency: 0,
            lastCheck: Date.now()
        };
    }

    /**
     * Record a disconnection event
     * @param {string} guildId - Guild ID
     * @param {string} reason - Reason for disconnection
     */
    recordDisconnection(guildId, reason = 'unknown') {
        if (!this.guildStats.has(guildId)) {
            this.guildStats.set(guildId, {
                disconnections: 0,
                reconnections: 0,
                lastDisconnection: null,
                consecutiveIssues: 0
            });
        }

        const stats = this.guildStats.get(guildId);
        stats.disconnections++;
        stats.lastDisconnection = { time: Date.now(), reason };
        stats.consecutiveIssues++;

        this.globalStats.totalDisconnections++;

        console.log(`[Network Monitor] Disconnection recorded for guild ${guildId}: ${reason}`);

        // Check if this guild is having frequent issues
        if (stats.consecutiveIssues >= 3) {
            console.warn(`[Network Monitor] Guild ${guildId} has ${stats.consecutiveIssues} consecutive issues. Network may be unstable.`);
        }
    }

    /**
     * Record a successful reconnection
     * @param {string} guildId - Guild ID
     */
    recordReconnection(guildId) {
        if (!this.guildStats.has(guildId)) {
            this.guildStats.set(guildId, {
                disconnections: 0,
                reconnections: 0,
                lastDisconnection: null,
                consecutiveIssues: 0
            });
        }

        const stats = this.guildStats.get(guildId);
        stats.reconnections++;
        stats.consecutiveIssues = 0; // Reset consecutive issues on successful reconnection

        this.globalStats.totalReconnections++;

        console.log(`[Network Monitor] Successful reconnection for guild ${guildId}`);
    }

    /**
     * Get stability report for a guild
     * @param {string} guildId - Guild ID
     * @returns {object} Stability report
     */
    getGuildReport(guildId) {
        const stats = this.guildStats.get(guildId);
        if (!stats) {
            return {
                status: 'stable',
                disconnections: 0,
                reconnections: 0,
                reliability: 100
            };
        }

        const totalEvents = stats.disconnections + stats.reconnections;
        const reliability = totalEvents > 0 ? Math.round((stats.reconnections / totalEvents) * 100) : 100;

        let status = 'stable';
        if (stats.consecutiveIssues >= 3) {
            status = 'unstable';
        } else if (stats.consecutiveIssues >= 1) {
            status = 'warning';
        }

        return {
            status,
            disconnections: stats.disconnections,
            reconnections: stats.reconnections,
            reliability,
            lastDisconnection: stats.lastDisconnection,
            consecutiveIssues: stats.consecutiveIssues
        };
    }

    /**
     * Create a diagnostic embed for network issues
     * @param {string} guildId - Guild ID
     * @returns {EmbedBuilder} Diagnostic embed
     */
    createDiagnosticEmbed(guildId) {
        const report = this.getGuildReport(guildId);
        
        let color = '#00FF00'; // Green for stable
        let statusEmoji = '✅';
        
        if (report.status === 'warning') {
            color = '#FFA500'; // Orange for warning
            statusEmoji = '⚠️';
        } else if (report.status === 'unstable') {
            color = '#FF0000'; // Red for unstable
            statusEmoji = '❌';
        }

        const embed = new EmbedBuilder()
            .setColor(color)
            .setTitle(`${statusEmoji} Network Stability Report`)
            .setDescription(`Connection status: **${report.status.toUpperCase()}**`)
            .addFields(
                { name: 'Disconnections', value: report.disconnections.toString(), inline: true },
                { name: 'Reconnections', value: report.reconnections.toString(), inline: true },
                { name: 'Reliability', value: `${report.reliability}%`, inline: true }
            )
            .setTimestamp();

        if (report.lastDisconnection) {
            const timeSince = Math.round((Date.now() - report.lastDisconnection.time) / 1000);
            embed.addFields({
                name: 'Last Issue',
                value: `${timeSince}s ago (${report.lastDisconnection.reason})`,
                inline: false
            });
        }

        if (report.status !== 'stable') {
            embed.addFields({
                name: 'Recommendations',
                value: this.getRecommendations(report),
                inline: false
            });
        }

        return embed;
    }

    /**
     * Get recommendations based on stability report
     * @param {object} report - Stability report
     * @returns {string} Recommendations
     */
    getRecommendations(report) {
        const recommendations = [];

        if (report.consecutiveIssues >= 3) {
            recommendations.push('• Check your internet connection stability');
            recommendations.push('• Consider restarting the bot');
            recommendations.push('• Verify Discord API status');
        }

        if (report.reliability < 80) {
            recommendations.push('• Check server network latency');
            recommendations.push('• Consider using a different audio quality setting');
        }

        if (recommendations.length === 0) {
            recommendations.push('• Monitor connection for further issues');
        }

        return recommendations.join('\n');
    }

    /**
     * Reset stats for a guild
     * @param {string} guildId - Guild ID
     */
    resetGuildStats(guildId) {
        this.guildStats.delete(guildId);
        console.log(`[Network Monitor] Reset stats for guild ${guildId}`);
    }

    /**
     * Get global network statistics
     * @returns {object} Global stats
     */
    getGlobalStats() {
        return { ...this.globalStats };
    }
}

// Create singleton instance
const networkMonitor = new NetworkStabilityMonitor();

module.exports = {
    NetworkStabilityMonitor,
    networkMonitor
};
