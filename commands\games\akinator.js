const { 
    <PERSON><PERSON>ow<PERSON><PERSON>er, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    <PERSON>bed<PERSON><PERSON><PERSON> 
  } = require("discord.js");
  const { Aki } = require("aki-api");
  
  module.exports = {
    name: "akinator",
    aliases: ["aki"],
    category: "game",
    description: "Play Akinator with back button and guess confirmation",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
      const aki = new Aki({ region: "en", childMode: false });
      let history = []; // Store previous steps for back button
      let confirmed = false;
  
      try {
        await aki.start();
      } catch (err) {
        return message.channel.send("Error starting Akinator.");
      }
  
      // Button Components ==============================================
      const createAnswerRow = () => new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId("0")
          .setLabel("Yes")
          .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
          .setCustomId("1")
          .setLabel("No")
          .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
          .setCustomId("2")
          .setLabel("Don't Know")
          .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
          .setCustomId("3")
          .setLabel("Probably")
          .setStyle(ButtonStyle.Primary),
        new ButtonBuilder()
          .setCustomId("4")
          .setLabel("Probably Not")
          .setStyle(ButtonStyle.Primary)
      );
  
      const createNavigationRow = (canGoBack = false) => {
        const row = new ActionRowBuilder();
        
        if(canGoBack) {
          row.addComponents(
            new ButtonBuilder()
              .setCustomId("back")
              .setLabel("◀ Back")
              .setStyle(ButtonStyle.Secondary)
          );
        }
  
        row.addComponents(
          new ButtonBuilder()
            .setCustomId("end")
            .setLabel("End Game")
            .setStyle(ButtonStyle.Danger)
        );
        
        return row;
      };
  
      const createConfirmationRow = () => new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId("correct")
          .setLabel("✅ Correct")
          .setStyle(ButtonStyle.Success),
        new ButtonBuilder()
          .setCustomId("wrong")
          .setLabel("❌ Wrong")
          .setStyle(ButtonStyle.Danger)
      );
      
      // Initial Game Setup =============================================
      const initialEmbed = new EmbedBuilder()
        .setColor("#0099ff")
        .setTitle("Akinator - Question 1")
        .setDescription(aki.question);
  
      const sentMessage = await message.channel.send({
        embeds: [initialEmbed],
        components: [createAnswerRow(), createNavigationRow()]
      });
  
      const collector = sentMessage.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: 600000
      });
  
      // Interaction Handler ===========================================
      collector.on("collect", async interaction => {
        await interaction.deferUpdate();
        if(confirmed) return;
  
        // Handle Special Buttons ======================================
        switch(interaction.customId) {
          case "end":
            collector.stop();
            return sentMessage.edit({
              content: "Game ended. Thanks for playing!",
              components: []
            });
  
          case "back":
            if(history.length > 0) {
              const prevState = history.pop();
              Object.assign(aki, prevState);
              
              const backEmbed = new EmbedBuilder()
                .setColor("#0099ff")
                .setTitle(`Akinator - Question ${aki.currentStep + 1}`)
                .setDescription(aki.question);
  
              return sentMessage.edit({
                embeds: [backEmbed],
                components: [
                  createAnswerRow(),
                  createNavigationRow(history.length > 0)
                ]
              });
            }
            return;
  
          case "correct":
            confirmed = true;
            collector.stop();
            return sentMessage.edit({
              content: "🎉 I knew it! Thanks for playing!",
              components: []
            });
  
          case "wrong":
            confirmed = true;
            collector.stop();
            return sentMessage.edit({
              content: "😞 I'll try better next time!",
              components: []
            });
        }
  
        // Handle Regular Answers ======================================
        try {
          // Save current state before proceeding
          history.push({
            session: { ...aki.session },
            progression: aki.progression,
            currentStep: aki.currentStep,
            question: aki.question
          });
  
          await aki.step(parseInt(interaction.customId));
        } catch (error) {
          collector.stop();
          return sentMessage.edit({
            content: `Error: ${error.message}`,
            components: []
          });
        }
  
        // Handle Guess ================================================
        if(aki.guess?.id_base_proposition) {
          const guessEmbed = new EmbedBuilder()
            .setColor("#00ff00")
            .setTitle(`Is this your character?`)
            .setDescription(`**${aki.guess.name_proposition}**\n${aki.guess.description_proposition}`)
            .setImage(aki.guess.photo);
  
          return sentMessage.edit({
            embeds: [guessEmbed],
            components: [createConfirmationRow()]
          });
        }
  
        // Continue with Next Question ================================
        const newEmbed = new EmbedBuilder()
          .setColor("#0099ff")
          .setTitle(`Akinator - Question ${aki.currentStep + 1}`)
          .setDescription(aki.question);
  
        await sentMessage.edit({
          embeds: [newEmbed],
          components: [
            createAnswerRow(),
            createNavigationRow(history.length > 0)
          ]
        });
      });
  
      collector.on("end", () => {
        if(!confirmed) {
          sentMessage.edit({
            components: []
          }).catch(() => {});
        }
      });
    }
  };