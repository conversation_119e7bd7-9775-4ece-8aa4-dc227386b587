const { AudioPlayerStatus } = require('@discordjs/voice');
const {
    createThemedEmbed,
    formatSongTitle,
    createStatusIndicator,
    createTimeProgressBar,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

module.exports = {
    name: 'nowplaying',
    description: 'Show information about the currently playing song',
    aliases: ['np', 'current', 'playing'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || !serverQueue.currentSong) {
            const noMusicEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Music Playing`)
                .setDescription(`${createStatusIndicator('error', 'There is no music currently playing!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to start playing music.`);
            return message.reply({ embeds: [noMusicEmbed] });
        }

        const currentSong = serverQueue.currentSong;
        const playerStatus = serverQueue.player.state.status;

        // Determine status emoji, text, and embed type
        let statusEmoji = EMOJIS.PLAY;
        let statusText = 'Playing';
        let embedType = 'playing';

        if (playerStatus === AudioPlayerStatus.Paused) {
            statusEmoji = EMOJIS.PAUSE;
            statusText = 'Paused';
            embedType = 'paused';
        } else if (playerStatus === AudioPlayerStatus.Buffering) {
            statusEmoji = EMOJIS.LOADING;
            statusText = 'Buffering';
            embedType = 'loading';
        } else if (playerStatus === AudioPlayerStatus.Idle) {
            statusEmoji = EMOJIS.STOP;
            statusText = 'Stopped';
            embedType = 'stopped';
        }

        // Calculate progress for progress bar
        let progressInfo = null;
        if (serverQueue.startTime && currentSong.duration && embedType === 'playing') {
            const currentTime = Date.now() - serverQueue.startTime;
            const currentSeconds = Math.floor(currentTime / 1000);

            // Parse duration to seconds (supports MM:SS and HH:MM:SS formats)
            const durationParts = currentSong.duration.split(':').map(Number);
            let totalSeconds = 0;
            if (durationParts.length === 2) {
                totalSeconds = durationParts[0] * 60 + durationParts[1]; // MM:SS
            } else if (durationParts.length === 3) {
                totalSeconds = durationParts[0] * 3600 + durationParts[1] * 60 + durationParts[2]; // HH:MM:SS
            }

            if (totalSeconds > 0 && currentSeconds <= totalSeconds) {
                const percentage = Math.min(100, (currentSeconds / totalSeconds) * 100);
                progressInfo = {
                    currentSeconds,
                    totalSeconds,
                    percentage,
                    progressBar: createTimeProgressBar(currentSeconds, totalSeconds)
                };
            }
        }

        // Calculate track position in queue
        const trackPosition = 1; // Current song is always position 1
        const totalTracks = serverQueue.songs.length;

        const nowPlayingEmbed = createThemedEmbed(embedType)
            .setTitle(`${EMOJIS.MUSIC_NOTE} Now ${statusText}`)
            .setDescription(`**${formatSongTitle(currentSong.title, currentSong.displayUrl || currentSong.url, embedType === 'playing')}**`)
            .setThumbnail(currentSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=🎵')
            .addFields(
                {
                    name: `${EMOJIS.USER} Artist`,
                    value: `${EMOJIS.MUSIC_NOTE} ${currentSong.uploader || 'Unknown Artist'}`,
                    inline: true
                },
                {
                    name: `${EMOJIS.QUEUE} Track Position`,
                    value: `${EMOJIS.SPARKLES} ${trackPosition} / ${totalTracks}`,
                    inline: true
                },
                {
                    name: `${EMOJIS.TIMER} Duration`,
                    value: `${EMOJIS.CLOCK} ${currentSong.duration || 'N/A'}`,
                    inline: true
                },
                {
                    name: `${EMOJIS.USER} Requested by`,
                    value: `${EMOJIS.HEART} ${currentSong.requestedBy || 'Unknown'}`,
                    inline: true
                },
                {
                    name: `${EMOJIS.MUSIC_NOTE} Status`,
                    value: `${statusEmoji} ${statusText}`,
                    inline: true
                }
            );

        // Add loop mode information (always show to match slash command structure)
        const loopText = serverQueue.loopMode === 'song' ? `${EMOJIS.REPEAT_ONE} Song` :
                        serverQueue.loopMode === 'queue' ? `${EMOJIS.REPEAT} Queue` : `${EMOJIS.ERROR} Off`;

        nowPlayingEmbed.addFields({
            name: `${EMOJIS.REPEAT} Loop Mode`,
            value: loopText,
            inline: true
        });

        // Add progress bar if available
        if (progressInfo) {
            nowPlayingEmbed.addFields({
                name: `${EMOJIS.TIMER} Progress`,
                value: `${progressInfo.progressBar}\n\`${Math.round(progressInfo.percentage)}%\``,
                inline: false
            });
        }

        // Add active filters if any
        if (currentSong.hasFilters && currentSong.activeFilters && currentSong.activeFilters.length > 0) {
            nowPlayingEmbed.addFields({
                name: `${EMOJIS.FILTER} Active Filters`,
                value: `${EMOJIS.SPARKLES} ${currentSong.activeFilters.join(', ')}`,
                inline: false
            });
        }

        // Add autoplay status if enabled
        if (serverQueue.autoplay) {
            nowPlayingEmbed.addFields({
                name: `${EMOJIS.SPARKLES} Autoplay`,
                value: `${EMOJIS.SUCCESS} Enabled - Related songs will be added automatically`,
                inline: false
            });
        }

        // Set footer to match slash command structure
        const queuedTracks = Math.max(0, serverQueue.songs.length - 1); // Exclude current song
        nowPlayingEmbed.setFooter({
            text: `Total songs: ${serverQueue.songs.length} | Queued: ${queuedTracks} | Powered by Rikai Music Bot`
        });

        // Add timestamp to match slash command
        nowPlayingEmbed.setTimestamp();

        message.reply({ embeds: [nowPlayingEmbed] });
    },
};
