const axios = require("axios");

module.exports = {
    name: 'gambar',
    aliases: ['image', 'gi'],
    category: 'utility',
    description: 'Cari gambar di Google (random)',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const query = args.join(" ");
        if (!query) {
            return message.reply("Please provide a search query for the image.");
        }

        const page = Math.floor(Math.random() * 2);

        try {
            const response = await axios.get(
                `https://serpapi.com/search?q=${encodeURIComponent(query)}&tbm=isch&ijn=${page}&engine=google&apikey=${process.env.serp}`
            );

            if (!response.data.images_results || response.data.images_results.length === 0) {
                return message.reply("No images found for your query.");
            }

            const image = response.data.images_results[
                Math.floor(Math.random() * response.data.images_results.length)
            ];

            const originalContentUrl = image.original;

            message.reply(originalContentUrl);

        } catch (error) {
            console.error(error);
            message.reply("An error occurred while fetching the image.");
        }
    },
};