const {
    createThemedEmbed,
    formatSongTitle,
    createStatusIndicator,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

module.exports = {
    name: 'skip',
    description: 'Skip the currently playing song',
    aliases: ['s', 'next'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || !serverQueue.playing || !serverQueue.currentSong) {
            const noMusicEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Music Playing`)
                .setDescription(`${createStatusIndicator('error', 'There is no music currently playing!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to start playing music.`);
            return message.reply({ embeds: [noMusicEmbed] });
        }

        const currentSong = serverQueue.currentSong;

        // Check if there are more songs in the queue
        if (serverQueue.songs.length <= 1) {
            const lastSongEmbed = createThemedEmbed('warning')
                .setTitle(`${EMOJIS.SKIP} Last Song`)
                .setDescription(`${createStatusIndicator('warning', 'Skipped the last song in queue')}\n\n${formatSongTitle(currentSong.title, currentSong.displayUrl || currentSong.url)}\n\n${EMOJIS.INFO} The music will stop.`)
                .setThumbnail(currentSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=🎵');

            // Stop the player to trigger the next song logic
            serverQueue.player.stop();

            return message.reply({ embeds: [lastSongEmbed] });
        }

        const nextSong = serverQueue.songs[1]; // Next song in queue

        const skipEmbed = createThemedEmbed('success')
            .setTitle(`${EMOJIS.SKIP} Song Skipped`)
            .setDescription(`${createStatusIndicator('success', 'Skipped current song')}\n\n${formatSongTitle(currentSong.title, currentSong.displayUrl || currentSong.url)}`)
            .setThumbnail(currentSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=🎵')
            .addFields({
                name: `${EMOJIS.SKIP} Up Next`,
                value: formatSongTitle(nextSong.title, nextSong.displayUrl || nextSong.url),
                inline: false
            });

        // Stop the current song to trigger playNext
        serverQueue.player.stop();

        message.reply({ embeds: [skipEmbed] });
    },
};
