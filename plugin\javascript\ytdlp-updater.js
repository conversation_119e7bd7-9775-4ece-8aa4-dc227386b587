/**
 * yt-dlp Auto-Updater Plugin
 * Automatically checks for and downloads the latest yt-dlp binary from GitHub
 * 
 * Features:
 * - Automatic version checking
 * - Download latest release from GitHub
 * - Backup old version before updating
 * - Scheduled updates
 * - Manual update command
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { exec } = require('child_process');
const { promisify } = require('util');

class YtDlpUpdater {
    constructor() {
        this.binPath = path.join(__dirname, '..', '..', 'bin');
        this.ytdlpPath = path.join(this.binPath, 'yt-dlp.exe');
        this.backupPath = path.join(this.binPath, 'yt-dlp-backup.exe');
        this.githubApiUrl = 'https://api.github.com/repos/yt-dlp/yt-dlp/releases/latest';
        this.versionFile = path.join(this.binPath, 'yt-dlp-version.txt');
        this.configFile = path.join(this.binPath, 'updater-config.json');

        // Load configuration
        this.config = this.loadConfig();

        // Auto-update settings
        this.autoUpdateEnabled = this.config.autoUpdateEnabled ?? true;
        this.checkInterval = this.config.checkInterval ?? (24 * 60 * 60 * 1000); // 24 hours default
        this.updateTimer = null;

        console.log('🔄 yt-dlp Auto-Updater initialized');
        this.init();
    }

    /**
     * Load configuration from file
     */
    loadConfig() {
        try {
            if (fs.existsSync(this.configFile)) {
                const configData = fs.readFileSync(this.configFile, 'utf8');
                return JSON.parse(configData);
            }
        } catch (error) {
            console.warn('⚠️ Failed to load updater config, using defaults:', error.message);
        }

        // Return default configuration
        return {
            autoUpdateEnabled: true,
            checkInterval: 24 * 60 * 60 * 1000, // 24 hours
            lastUpdateCheck: null,
            lastUpdateVersion: null
        };
    }

    /**
     * Save configuration to file
     */
    saveConfig() {
        try {
            fs.writeFileSync(this.configFile, JSON.stringify(this.config, null, 2));
        } catch (error) {
            console.error('❌ Failed to save updater config:', error.message);
        }
    }

    /**
     * Initialize the updater
     */
    async init() {
        try {
            // Ensure bin directory exists
            if (!fs.existsSync(this.binPath)) {
                fs.mkdirSync(this.binPath, { recursive: true });
                console.log('📁 Created bin directory');
            }

            // Check current version
            const currentVersion = await this.getCurrentVersion();
            console.log(`📦 Current yt-dlp version: ${currentVersion || 'Unknown'}`);

            // Start auto-update timer if enabled
            if (this.autoUpdateEnabled) {
                this.startAutoUpdateTimer();
            }

            // Check for updates on startup (non-blocking)
            setTimeout(() => this.checkForUpdates(), 5000);

        } catch (error) {
            console.error('❌ Failed to initialize yt-dlp updater:', error.message);
        }
    }

    /**
     * Get current yt-dlp version
     */
    async getCurrentVersion() {
        try {
            if (!fs.existsSync(this.ytdlpPath)) {
                return null;
            }

            // Try to get version from version file first
            if (fs.existsSync(this.versionFile)) {
                const savedVersion = fs.readFileSync(this.versionFile, 'utf8').trim();
                if (savedVersion) return savedVersion;
            }

            // Fallback: get version from yt-dlp binary
            const execAsync = promisify(exec);
            const { stdout } = await execAsync(`"${this.ytdlpPath}" --version`, { timeout: 10000 });
            const version = stdout.trim();
            
            // Save version to file for faster future checks
            fs.writeFileSync(this.versionFile, version);
            return version;

        } catch (error) {
            console.warn('⚠️ Could not determine yt-dlp version:', error.message);
            return null;
        }
    }

    /**
     * Get latest version from GitHub
     */
    async getLatestVersion() {
        return new Promise((resolve, reject) => {
            const request = https.get(this.githubApiUrl, {
                headers: {
                    'User-Agent': 'yt-dlp-updater-bot'
                }
            }, (response) => {
                let data = '';

                response.on('data', (chunk) => {
                    data += chunk;
                });

                response.on('end', () => {
                    try {
                        const release = JSON.parse(data);
                        resolve({
                            version: release.tag_name,
                            downloadUrl: release.assets.find(asset => 
                                asset.name === 'yt-dlp.exe'
                            )?.browser_download_url,
                            publishedAt: release.published_at
                        });
                    } catch (error) {
                        reject(new Error('Failed to parse GitHub API response'));
                    }
                });
            });

            request.on('error', (error) => {
                reject(new Error(`GitHub API request failed: ${error.message}`));
            });

            request.setTimeout(15000, () => {
                request.destroy();
                reject(new Error('GitHub API request timeout'));
            });
        });
    }

    /**
     * Download file from URL
     */
    async downloadFile(url, destination) {
        return new Promise((resolve, reject) => {
            const file = fs.createWriteStream(destination);
            
            const request = https.get(url, (response) => {
                if (response.statusCode !== 200) {
                    reject(new Error(`Download failed with status: ${response.statusCode}`));
                    return;
                }

                const totalSize = parseInt(response.headers['content-length'], 10);
                let downloadedSize = 0;

                response.on('data', (chunk) => {
                    downloadedSize += chunk.length;
                    const progress = ((downloadedSize / totalSize) * 100).toFixed(1);
                    process.stdout.write(`\r📥 Downloading yt-dlp: ${progress}%`);
                });

                response.pipe(file);

                file.on('finish', () => {
                    file.close();
                    console.log('\n✅ Download completed');
                    resolve();
                });
            });

            request.on('error', (error) => {
                fs.unlink(destination, () => {}); // Clean up partial file
                reject(new Error(`Download failed: ${error.message}`));
            });

            request.setTimeout(300000, () => { // 5 minutes timeout
                request.destroy();
                fs.unlink(destination, () => {});
                reject(new Error('Download timeout'));
            });
        });
    }

    /**
     * Create backup of current yt-dlp
     */
    createBackup() {
        try {
            if (fs.existsSync(this.ytdlpPath)) {
                fs.copyFileSync(this.ytdlpPath, this.backupPath);
                console.log('💾 Created backup of current yt-dlp');
                return true;
            }
        } catch (error) {
            console.error('❌ Failed to create backup:', error.message);
        }
        return false;
    }

    /**
     * Restore from backup
     */
    restoreBackup() {
        try {
            if (fs.existsSync(this.backupPath)) {
                fs.copyFileSync(this.backupPath, this.ytdlpPath);
                console.log('🔄 Restored yt-dlp from backup');
                return true;
            }
        } catch (error) {
            console.error('❌ Failed to restore backup:', error.message);
        }
        return false;
    }

    /**
     * Check for updates
     */
    async checkForUpdates(force = false) {
        try {
            console.log('🔍 Checking for yt-dlp updates...');

            const [currentVersion, latestRelease] = await Promise.all([
                this.getCurrentVersion(),
                this.getLatestVersion()
            ]);

            // Update last check time
            this.config.lastUpdateCheck = new Date().toISOString();
            this.saveConfig();

            if (!latestRelease.downloadUrl) {
                throw new Error('No Windows executable found in latest release');
            }

            const needsUpdate = !currentVersion || currentVersion !== latestRelease.version;

            if (needsUpdate || force) {
                console.log(`🆕 Update available: ${currentVersion || 'None'} → ${latestRelease.version}`);
                return await this.performUpdate(latestRelease);
            } else {
                console.log('✅ yt-dlp is up to date');
                return { success: true, message: 'Already up to date', version: currentVersion };
            }

        } catch (error) {
            console.error('❌ Update check failed:', error.message);
            return { success: false, error: error.message };
        }
    }

    /**
     * Perform the actual update
     */
    async performUpdate(release) {
        try {
            console.log(`🚀 Updating yt-dlp to version ${release.version}...`);

            // Create backup
            this.createBackup();

            // Download new version to temporary file
            const tempPath = path.join(this.binPath, 'yt-dlp-temp.exe');
            await this.downloadFile(release.downloadUrl, tempPath);

            // Replace old version with new one
            if (fs.existsSync(this.ytdlpPath)) {
                fs.unlinkSync(this.ytdlpPath);
            }
            fs.renameSync(tempPath, this.ytdlpPath);

            // Update version file
            fs.writeFileSync(this.versionFile, release.version);

            // Verify the update
            const newVersion = await this.getCurrentVersion();
            if (newVersion === release.version) {
                console.log(`✅ Successfully updated yt-dlp to version ${release.version}`);

                // Update configuration
                this.config.lastUpdateCheck = new Date().toISOString();
                this.config.lastUpdateVersion = release.version;
                this.saveConfig();

                // Clean up backup after successful update
                setTimeout(() => {
                    if (fs.existsSync(this.backupPath)) {
                        fs.unlinkSync(this.backupPath);
                    }
                }, 60000); // Keep backup for 1 minute

                return {
                    success: true,
                    message: `Updated to version ${release.version}`,
                    version: release.version
                };
            } else {
                throw new Error('Version verification failed after update');
            }

        } catch (error) {
            console.error('❌ Update failed:', error.message);
            
            // Restore backup on failure
            if (this.restoreBackup()) {
                console.log('🔄 Restored previous version');
            }

            return { success: false, error: error.message };
        }
    }

    /**
     * Start auto-update timer
     */
    startAutoUpdateTimer() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);
            // Remove old timer from tracking if client is available
            if (global.client && global.client.activeIntervals) {
                global.client.activeIntervals.delete(this.updateTimer);
            }
        }

        this.updateTimer = setInterval(() => {
            console.log('⏰ Scheduled yt-dlp update check...');
            this.checkForUpdates();
        }, this.checkInterval);

        // Track timer for cleanup if client is available
        if (global.client && global.client.activeIntervals) {
            global.client.activeIntervals.add(this.updateTimer);
        }

        console.log(`⏰ Auto-update scheduled every ${this.checkInterval / (60 * 60 * 1000)} hours`);
    }

    /**
     * Stop auto-update timer
     */
    stopAutoUpdateTimer() {
        if (this.updateTimer) {
            clearInterval(this.updateTimer);

            // Remove from tracking if client is available
            if (global.client && global.client.activeIntervals) {
                global.client.activeIntervals.delete(this.updateTimer);
            }

            this.updateTimer = null;
            console.log('⏹️ Auto-update timer stopped');
        }
    }

    /**
     * Manual update trigger
     */
    async manualUpdate(force = false) {
        console.log('🔧 Manual update triggered...');
        return await this.checkForUpdates(force);
    }

    /**
     * Get updater status
     */
    getStatus() {
        return {
            autoUpdateEnabled: this.autoUpdateEnabled,
            checkInterval: this.checkInterval,
            binPath: this.binPath,
            ytdlpExists: fs.existsSync(this.ytdlpPath),
            backupExists: fs.existsSync(this.backupPath),
            lastUpdateCheck: this.config.lastUpdateCheck,
            lastUpdateVersion: this.config.lastUpdateVersion,
            configFile: this.configFile
        };
    }
}

// Export the updater class and create a singleton instance
const updaterInstance = new YtDlpUpdater();

module.exports = {
    YtDlpUpdater,
    updater: updaterInstance
};
