const { exec } = require('child_process');
const { createAudioResource } = require('@discordjs/voice');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const { Innertube } = require('youtubei.js');
require('dotenv').config();

/**
 * Streaming utilities for Discord music bot
 * Provides direct audio streaming without file downloads
 *
 * IMPROVEMENTS MADE:
 * - Replaced deprecated --get-* commands with modern --print method
 * - Single command call for all metadata extraction (more efficient)
 * - Better error handling with specific error messages
 * - Improved output parsing with fallback handling
 * - Enhanced cookie support with modern yt-dlp syntax
 * - More robust URL validation and ID extraction
 */

// Path to local yt-dlp binary
const YT_DLP_PATH = path.join(__dirname, '..', 'bin', 'yt-dlp.exe');

// Get YouTube cookies from environment
const YOUTUBE_COOKIES = process.env.cookie;

// Initialize YouTube service for metadata extraction
let innertube;
(async () => {
    try {
        innertube = await Innertube.create();
    } catch (error) {
        console.error('❌ Failed to initialize YouTube metadata service:', error);
    }
})();

// Verify yt-dlp binary exists
if (!fs.existsSync(YT_DLP_PATH)) {
    console.error(`❌ yt-dlp binary not found at: ${YT_DLP_PATH}`);
    console.error('Please ensure yt-dlp.exe is placed in the bin folder');
}

/**
 * Build yt-dlp command with cookies if available
 * @param {string} baseCommand - Base yt-dlp command
 * @param {string} url - Video URL
 * @param {boolean} useCookies - Whether to use cookies (default: false for better reliability)
 * @returns {string} Complete command with cookies
 */
function buildYtDlpCommand(baseCommand, url, useCookies = false) {
    let command = `"${YT_DLP_PATH}" ${baseCommand}`;

    // Add cookies if available and requested for YouTube URLs
    if (useCookies && YOUTUBE_COOKIES && url.includes('youtube.com')) {
        // Create a temporary cookies file in proper Netscape format
        const cookiesFile = path.join(__dirname, '..', 'temp_cookies.txt');
        try {
            // Parse the cookie string and create proper Netscape format
            const cookies = YOUTUBE_COOKIES.split(';').map(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && value) {
                    return `.youtube.com\tTRUE\t/\tFALSE\t0\t${name}\t${value}`;
                }
                return null;
            }).filter(Boolean);

            const cookieContent = `# Netscape HTTP Cookie File\n${cookies.join('\n')}`;
            fs.writeFileSync(cookiesFile, cookieContent);
            command += ` --cookies "${cookiesFile}"`;
        } catch (error) {
            console.warn('Failed to create cookies file:', error.message);
        }
    }

    command += ` "${url}"`;
    return command;
}

/**
 * Execute yt-dlp command with retry logic and better error handling
 * @param {string} command - Command to execute
 * @param {number} timeout - Timeout in milliseconds
 * @param {number} retries - Number of retries
 * @returns {Promise<string>} Command output
 */
function executeYtDlpCommand(command, timeout = 15000, retries = 2) {
    return new Promise((resolve, reject) => {
        const attemptCommand = (attempt) => {
            exec(command, { timeout }, (error, stdout, stderr) => {
                // Check if there's actual output even with warnings
                if (stdout && stdout.trim()) {
                    // Log warnings but don't treat them as errors (only first warning)
                    if (stderr && stderr.includes('WARNING') && !stderr.includes('DRM protected')) {
                        console.warn('yt-dlp warning:', stderr.split('\n')[0]);
                    }
                    resolve(stdout.trim());
                    return;
                }

                if (error) {
                    // Provide more specific error messages based on stderr
                    let errorMessage = error.message;
                    if (stderr) {
                        if (stderr.includes('Video unavailable')) {
                            errorMessage = 'Video is unavailable or has been removed';
                        } else if (stderr.includes('Private video')) {
                            errorMessage = 'Video is private and cannot be accessed';
                        } else if (stderr.includes('region')) {
                            errorMessage = 'Video is not available in your region';
                        } else if (stderr.includes('age-restricted')) {
                            errorMessage = 'Video is age-restricted and requires authentication';
                        } else if (stderr.includes('Sign in to confirm')) {
                            errorMessage = 'Video requires sign-in to access';
                        } else if (stderr.includes('HTTP Error 429')) {
                            errorMessage = 'Rate limited by the service, please try again later';
                        }
                    }

                    if (attempt < retries) {
                        console.warn(`yt-dlp attempt ${attempt + 1} failed: ${errorMessage}, retrying...`);
                        setTimeout(() => attemptCommand(attempt + 1), 1000);
                        return;
                    }
                    reject(new Error(errorMessage));
                    return;
                }

                // No output and no error - treat as failure
                if (attempt < retries) {
                    console.warn(`yt-dlp attempt ${attempt + 1} returned no output, retrying...`);
                    setTimeout(() => attemptCommand(attempt + 1), 1000);
                    return;
                }
                reject(new Error('yt-dlp returned no output'));
            });
        };
        attemptCommand(0);
    });
}

/**
 * Get direct streaming URL from yt-dlp using modern --print method
 * @param {string} url - Video URL
 * @returns {Promise<object>} Stream info with URL and metadata
 */
async function getStreamingUrl(url) {
    try {
        // Use modern --print method to get all metadata in one call
        // This is more reliable than deprecated --get-* commands
        const command = buildYtDlpCommand(
            '-f "bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio" ' +
            '--print "%(title)s" ' +
            '--print "%(duration_string)s" ' +
            '--print "%(thumbnail)s" ' +
            '--print "%(id)s" ' +
            '--get-url',
            url
        );

        const output = await executeYtDlpCommand(command, 20000);
        const lines = output.split('\n').filter(line => line.trim());

        if (lines.length < 2) {
            throw new Error('Invalid yt-dlp output for streaming');
        }

        // Modern yt-dlp output format with --print:
        // Line 0: title
        // Line 1: duration_string
        // Line 2: thumbnail
        // Line 3: id
        // Line 4: streaming_url (from --get-url)
        const title = lines[0] || 'Unknown Title';
        const duration = lines[1] || 'Unknown';
        const thumbnail = lines[2] && lines[2] !== 'NA' ? lines[2] : null;
        const id = lines[3] || 'unknown';
        const streamingUrl = lines[4] || lines[lines.length - 1]; // Fallback to last line

        return {
            streamingUrl,
            title,
            duration,
            thumbnail,
            id,
            isStream: true
        };
    } catch (error) {
        console.error('yt-dlp streaming error:', error.message);
        throw error;
    }
}

/**
 * Create audio resource from streaming URL with optional seek and filters
 * @param {string} streamingUrl - Direct streaming URL
 * @param {object} options - Additional options (volume, seek, filters, metadata)
 * @returns {AudioResource} Discord audio resource
 */
function createStreamingResource(streamingUrl, options = {}) {
    try {
        // Base FFmpeg arguments for streaming
        const ffmpegArgs = [
            '-reconnect', '1',
            '-reconnect_streamed', '1',
            '-reconnect_delay_max', '5'
        ];

        // Add seek parameter if specified
        if (options.seek && options.seek > 0) {
            ffmpegArgs.push('-ss', options.seek.toString());
        }

        // Add input URL
        ffmpegArgs.push('-i', streamingUrl);

        // Add audio filters if specified
        if (options.filters && options.filters.length > 0) {
            ffmpegArgs.push('-af', options.filters.join(','));
        }

        // Output format with timing fixes
        ffmpegArgs.push(
            '-analyzeduration', '0',
            '-loglevel', '0',
            '-f', 's16le',
            '-ar', '48000',
            '-ac', '2',
            '-acodec', 'pcm_s16le',
            '-avoid_negative_ts', 'make_zero',
            '-fflags', '+genpts',
            'pipe:1'
        );

        const ffmpegProcess = spawn('ffmpeg', ffmpegArgs, {
            stdio: ['pipe', 'pipe', 'pipe']
        });

        // Handle FFmpeg errors (suppress normal termination warnings)
        ffmpegProcess.stderr.on('data', (data) => {
            const errorMsg = data.toString();
            // Only log actual errors, not normal termination messages
            if (!errorMsg.includes('Error submitting a packet') &&
                !errorMsg.includes('Error muxing a packet') &&
                !errorMsg.includes('Terminating thread') &&
                !errorMsg.includes('Error writing trailer') &&
                !errorMsg.includes('Error closing file')) {
                console.warn('FFmpeg stderr:', errorMsg);
            }
        });

        ffmpegProcess.on('error', (error) => {
            console.error('FFmpeg process error:', error);
        });

        // Track process for cleanup
        if (global.client && global.client.activeProcesses) {
            global.client.activeProcesses.add(ffmpegProcess);
        }

        // Handle process cleanup to prevent timing issues
        ffmpegProcess.on('exit', (code, signal) => {
            // Remove from tracking
            if (global.client && global.client.activeProcesses) {
                global.client.activeProcesses.delete(ffmpegProcess);
            }

            if (code !== null && code !== 0 && signal !== 'SIGTERM' && signal !== 'SIGKILL') {
                console.warn(`FFmpeg process exited with code ${code}, signal ${signal}`);
            }
        });

        // Create audio resource from FFmpeg output with better timing
        const resource = createAudioResource(ffmpegProcess.stdout, {
            inputType: 'raw',
            inlineVolume: true,
            metadata: options.metadata || {},
            silencePaddingFrames: 5
        });

        // Set volume if provided
        if (options.volume !== undefined) {
            resource.volume?.setVolume(options.volume / 100);
        }

        return resource;
    } catch (error) {
        console.error('Error creating streaming resource:', error);
        throw error;
    }
}

/**
 * Create audio resource with fallback to download if streaming fails
 * @param {object} songInfo - Song information
 * @param {object} options - Options including volume
 * @returns {Promise<AudioResource>} Audio resource
 */
async function createAudioResourceWithFallback(songInfo, options = {}) {
    try {
        // Try streaming first
        if (songInfo.streamingUrl) {
            return createStreamingResource(songInfo.streamingUrl, options);
        }

        // Fallback to getting streaming URL
        const streamInfo = await getStreamingUrl(songInfo.url);
        return createStreamingResource(streamInfo.streamingUrl, options);

    } catch (error) {
        console.warn(`Streaming failed for ${songInfo.title}, falling back to download:`, error.message);
        
        // Fallback to file-based playback
        if (songInfo.filePath && require('fs').existsSync(songInfo.filePath)) {
            return createAudioResource(songInfo.filePath, {
                inlineVolume: true,
                metadata: options.metadata || {}
            });
        }

        throw new Error('Both streaming and file playback failed');
    }
}

/**
 * Get YouTube video metadata using youtubei with enhanced error handling and fallbacks
 * @param {string} videoId - YouTube video ID
 * @returns {Promise<object>} Video metadata
 */
async function getYouTubeMetadata(videoId) {
    if (!innertube) {
        throw new Error('YouTube metadata service not available');
    }

    try {
        // Try to get video info with retry mechanism
        let info;
        let lastError;

        // Multiple attempts with different strategies
        const strategies = [
            { name: 'default', method: () => innertube.getInfo(videoId) },
            { name: 'basic_info', method: () => innertube.getBasicInfo(videoId) },
            { name: 'with_retry', method: () => innertube.getInfo(videoId, { parse: true }) }
        ];

        for (const strategy of strategies) {
            try {
                info = await strategy.method();
                if (info && (info.basic_info || info.videoDetails || info.title)) {
                    break;
                }
            } catch (error) {
                lastError = error;
                console.warn(`❌ Strategy ${strategy.name} failed: ${error.message}`);
                continue;
            }
        }

        if (!info) {
            throw lastError || new Error('No video information available from any strategy');
        }

        // Extract metadata with multiple fallback paths
        const metadata = extractVideoMetadata(info, videoId);

        return metadata;

    } catch (error) {
        console.warn(`❌ Failed to get YouTube metadata for ${videoId}: ${error.message}`);
        throw error;
    }
}

/**
 * Extract video metadata from YouTube.js response with comprehensive fallbacks
 * @param {object} info - YouTube.js video info response
 * @param {string} videoId - Video ID for fallback
 * @returns {object} Extracted metadata
 */
function extractVideoMetadata(info, videoId) {
    // Try multiple paths for basic info - handle both getInfo and getBasicInfo responses
    const basicInfo = info.basic_info || info.videoDetails || info || {};
    const playerResponse = info.player_response || {};
    const videoDetails = playerResponse.videoDetails || info.videoDetails || {};

    // Extract title with multiple fallbacks
    let title = 'Unknown Title';
    if (basicInfo.title) {
        title = typeof basicInfo.title === 'string' ? basicInfo.title :
               (basicInfo.title.text || basicInfo.title.simpleText || basicInfo.title.runs?.[0]?.text || 'Unknown Title');
    } else if (videoDetails.title) {
        title = videoDetails.title;
    } else if (info.title) {
        title = typeof info.title === 'string' ? info.title :
               (info.title.text || info.title.simpleText || info.title.runs?.[0]?.text || 'Unknown Title');
    }

    // Extract duration with multiple fallbacks
    let durationString = 'Unknown';
    let totalSeconds = null;

    // Try basic_info duration
    if (basicInfo.duration) {
        if (typeof basicInfo.duration === 'number') {
            totalSeconds = basicInfo.duration;
        } else if (basicInfo.duration.seconds_total) {
            totalSeconds = basicInfo.duration.seconds_total;
        } else if (basicInfo.duration.text) {
            durationString = basicInfo.duration.text;
        }
    }

    // Try videoDetails duration
    if (!totalSeconds && videoDetails.lengthSeconds) {
        totalSeconds = parseInt(videoDetails.lengthSeconds);
    }

    // Try alternative duration paths
    if (!totalSeconds && info.duration) {
        if (typeof info.duration === 'number') {
            totalSeconds = info.duration;
        } else if (info.duration.seconds_total) {
            totalSeconds = info.duration.seconds_total;
        }
    }

    // Format duration if we have seconds
    if (totalSeconds && !isNaN(totalSeconds) && totalSeconds > 0) {
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = Math.floor(totalSeconds % 60);

        if (hours > 0) {
            durationString = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            durationString = `${minutes}:${seconds.toString().padStart(2, '0')}`;
        }
    }

    // Extract thumbnail with multiple fallbacks
    let thumbnail = null;
    const thumbnailSources = [
        basicInfo.thumbnail,
        videoDetails.thumbnail?.thumbnails,
        info.thumbnail,
        playerResponse.videoDetails?.thumbnail?.thumbnails
    ].filter(Boolean);

    for (const thumbnailSource of thumbnailSources) {
        if (Array.isArray(thumbnailSource) && thumbnailSource.length > 0) {
            // Sort by quality (width/height) and get the best one
            const sortedThumbnails = thumbnailSource
                .filter(thumb => thumb && thumb.url)
                .sort((a, b) => {
                    const aQuality = (a.width || 0) * (a.height || 0);
                    const bQuality = (b.width || 0) * (b.height || 0);
                    return bQuality - aQuality;
                });

            if (sortedThumbnails.length > 0) {
                thumbnail = sortedThumbnails[0].url;
                break;
            }
        }
    }

    // Extract author/uploader with multiple fallbacks
    let uploader = 'Unknown';
    if (basicInfo.author) {
        uploader = typeof basicInfo.author === 'string' ? basicInfo.author :
                  (basicInfo.author.name || basicInfo.author.text || basicInfo.author.simpleText ||
                   basicInfo.author.runs?.[0]?.text || 'Unknown');
    } else if (videoDetails.author) {
        uploader = videoDetails.author;
    } else if (basicInfo.channel) {
        uploader = typeof basicInfo.channel === 'string' ? basicInfo.channel :
                  (basicInfo.channel.name || basicInfo.channel.text || basicInfo.channel.simpleText ||
                   basicInfo.channel.runs?.[0]?.text || 'Unknown');
    } else if (info.author) {
        uploader = typeof info.author === 'string' ? info.author :
                  (info.author.name || info.author.text || info.author.simpleText ||
                   info.author.runs?.[0]?.text || 'Unknown');
    }

    // Extract view count with fallbacks
    let viewCount = 0;
    if (basicInfo.view_count) {
        viewCount = parseInt(basicInfo.view_count) || 0;
    } else if (videoDetails.viewCount) {
        viewCount = parseInt(videoDetails.viewCount) || 0;
    }

    // Extract live status
    const isLive = !!(basicInfo.is_live || videoDetails.isLiveContent || basicInfo.is_live_content);

    // Extract private status
    const isPrivate = !!(basicInfo.is_private || videoDetails.isPrivate);

    return {
        id: videoId,
        title: title.trim(),
        duration: durationString,
        thumbnail: thumbnail,
        uploader: uploader.trim(),
        viewCount: viewCount,
        isLive: isLive,
        isPrivate: isPrivate,
        source: 'YouTube'
    };
}

/**
 * Get streaming URL only using yt-dlp (no metadata extraction)
 * @param {string} url - Video URL
 * @returns {Promise<string>} Direct streaming URL
 */
async function getStreamingUrlOnly(url) {
    // Simplified strategies focused only on getting the streaming URL
    const strategies = [
        {
            name: 'audio_only_no_cookies',
            useCookies: false,
            timeout: 15000,
            format: 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio'
        },
        {
            name: 'audio_only_with_cookies',
            useCookies: true,
            timeout: 20000,
            format: 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio'
        },
        {
            name: 'fallback_any_audio',
            useCookies: false,
            timeout: 25000,
            format: 'bestaudio/best[height<=720]'
        }
    ];

    let lastError = null;

    for (const strategy of strategies) {
        // Skip cookie strategies if no cookies available
        if (strategy.useCookies && (!YOUTUBE_COOKIES || !url.includes('youtube.com'))) {
            continue;
        }

        try {
            // Build command to get only the streaming URL
            const command = buildYtDlpCommand(
                `-f "${strategy.format}" --get-url --no-warnings --ignore-errors`,
                url,
                strategy.useCookies
            );

            const output = await executeYtDlpCommand(command, strategy.timeout, 1);
            const streamingUrl = output.trim();

            // Validate streaming URL
            if (!streamingUrl || !streamingUrl.startsWith('http')) {
                throw new Error(`No valid streaming URL found (got: ${streamingUrl})`);
            }

            // Additional validation for YouTube URLs
            if (url.includes('youtube.com') && !streamingUrl.includes('googlevideo.com') && !streamingUrl.includes('youtube.com')) {
                throw new Error('Invalid YouTube streaming URL format');
            }

            return streamingUrl;

        } catch (error) {
            lastError = error;
            console.warn(`❌ Streaming URL strategy ${strategy.name} failed: ${error.message}`);

            // Clean up cookies file if it exists
            if (strategy.useCookies) {
                const cookiesFile = path.join(__dirname, '..', 'temp_cookies.txt');
                if (fs.existsSync(cookiesFile)) {
                    try {
                        fs.unlinkSync(cookiesFile);
                    } catch (cleanupError) {
                        // Ignore cleanup errors
                    }
                }
            }

            continue;
        }
    }

    // All strategies failed
    console.error(`❌ All streaming URL strategies failed for: ${url}`);
    throw new Error('Could not get streaming URL from this source.');
}

/**
 * Get song info with streaming URL - New hybrid approach using youtubei + yt-dlp
 * @param {string} url - Video URL
 * @returns {Promise<object>} Song info with streaming capabilities
 */
async function getSongInfoWithStreaming(url) {
    // Check if it's a YouTube URL
    const youtubeMatch = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/);

    if (youtubeMatch && innertube) {
        // Use hybrid approach for YouTube: youtubei for metadata + yt-dlp for streaming URL
        const videoId = youtubeMatch[1];

        try {
            // Get metadata from youtubei and streaming URL from yt-dlp in parallel
            const [metadata, streamingUrl] = await Promise.all([
                getYouTubeMetadata(videoId),
                getStreamingUrlOnly(url)
            ]);

            const songInfo = {
                id: metadata.id,
                title: metadata.title,
                url: url,
                streamingUrl: streamingUrl,
                duration: metadata.duration,
                thumbnail: metadata.thumbnail,
                uploader: metadata.uploader,
                viewCount: metadata.viewCount,
                source: 'YouTube',
                isStream: true,
                canStream: true,
                extractionMethod: 'hybrid_youtubei_ytdlp'
            };

            return songInfo;

        } catch (error) {
            console.warn(`❌ Hybrid approach failed for ${videoId}: ${error.message}`);
            // Fall back to the old method
        }
    }

    // Fallback to original yt-dlp method for non-YouTube URLs or when hybrid fails
    return await getSongInfoWithStreamingFallback(url);
}

/**
 * Fallback method using yt-dlp for both metadata and streaming URL
 * @param {string} url - Video URL
 * @returns {Promise<object>} Song info with streaming capabilities
 */
async function getSongInfoWithStreamingFallback(url) {
    // Enhanced extraction strategies
    const strategies = [
        {
            name: 'modern_no_cookies',
            useCookies: false,
            timeout: 15000,
            retries: 1,
            format: 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio'
        },
        {
            name: 'modern_with_cookies',
            useCookies: true,
            timeout: 20000,
            retries: 1,
            format: 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio'
        },
        {
            name: 'fallback_any_audio',
            useCookies: false,
            timeout: 25000,
            retries: 2,
            format: 'bestaudio/best[height<=720]'
        }
    ];

    let lastError = null;

    for (const strategy of strategies) {
        // Skip cookie strategies if no cookies available
        if (strategy.useCookies && (!YOUTUBE_COOKIES || !url.includes('youtube.com'))) {
            continue;
        }

        try {
            // Build command with current strategy
            const command = buildYtDlpCommand(
                `-f "${strategy.format}" ` +
                '--print "%(title)s" ' +
                '--print "%(duration_string)s" ' +
                '--print "%(thumbnail)s" ' +
                '--print "%(id)s" ' +
                '--get-url ' +
                '--no-warnings ' +
                '--ignore-errors',
                url,
                strategy.useCookies
            );

            const output = await executeYtDlpCommand(command, strategy.timeout, strategy.retries);
            const lines = output.split('\n').filter(line => line.trim());

            if (lines.length < 2) {
                throw new Error(`Invalid yt-dlp output - insufficient data (${lines.length} lines)`);
            }

            // Parse yt-dlp output with better validation
            const title = lines[0] && lines[0] !== 'NA' && lines[0] !== 'null' ? lines[0] : 'Unknown Title';
            const duration = lines[1] && lines[1] !== 'NA' && lines[1] !== 'null' ? lines[1] : 'Unknown';
            const thumbnail = lines[2] && lines[2] !== 'NA' && lines[2] !== 'null' && lines[2].startsWith('http') ? lines[2] : null;
            const id = lines[3] && lines[3] !== 'NA' && lines[3] !== 'null' ? lines[3] : extractIdFromUrl(url);
            const streamingUrl = lines[4] || lines[lines.length - 1];

            // Validate streaming URL
            if (!streamingUrl || !streamingUrl.startsWith('http')) {
                throw new Error(`No valid streaming URL found (got: ${streamingUrl})`);
            }

            // Additional validation for YouTube URLs
            if (url.includes('youtube.com') && !streamingUrl.includes('googlevideo.com') && !streamingUrl.includes('youtube.com')) {
                throw new Error('Invalid YouTube streaming URL format');
            }

            const songInfo = {
                id: id,
                title: title,
                url: url,
                streamingUrl: streamingUrl,
                duration: duration,
                thumbnail: thumbnail,
                source: url.includes('youtube.com') ? 'YouTube' : 'Other',
                isStream: true,
                canStream: true,
                extractionMethod: 'ytdlp_fallback'
            };

            return songInfo;

        } catch (error) {
            lastError = error;
            console.warn(`❌ Fallback strategy ${strategy.name} failed for ${url}: ${error.message}`);

            // Clean up cookies file if it exists
            if (strategy.useCookies) {
                const cookiesFile = path.join(__dirname, '..', 'temp_cookies.txt');
                if (fs.existsSync(cookiesFile)) {
                    try {
                        fs.unlinkSync(cookiesFile);
                    } catch (cleanupError) {
                        // Ignore cleanup errors
                    }
                }
            }

            // Continue to next strategy
            continue;
        }
    }

    // All strategies failed
    console.error(`❌ All extraction strategies failed for: ${url}`);

    // Provide more specific error messages based on the URL and last error
    let errorMessage = 'Could not extract streaming URL from this source.';

    if (lastError) {
        if (lastError.message.includes('Video unavailable')) {
            errorMessage = 'Video is unavailable or has been removed from the platform.';
        } else if (lastError.message.includes('Private video')) {
            errorMessage = 'Video is private and cannot be accessed.';
        } else if (lastError.message.includes('region')) {
            errorMessage = 'Video is not available in your region.';
        } else if (lastError.message.includes('age-restricted')) {
            errorMessage = 'Video is age-restricted and requires authentication.';
        } else if (lastError.message.includes('Sign in to confirm')) {
            errorMessage = 'Video requires sign-in to access.';
        } else if (lastError.message.includes('HTTP Error 429')) {
            errorMessage = 'Rate limited by the service. Please try again later.';
        } else if (lastError.message.includes('DRM protected')) {
            errorMessage = 'Video is DRM protected and cannot be downloaded.';
        }
    }

    throw new Error(errorMessage);
}

/**
 * Extract video ID from URL (fallback method)
 * @param {string} url - Video URL
 * @returns {string} Video ID or 'unknown'
 */
function extractIdFromUrl(url) {
    // YouTube video ID extraction
    const youtubeMatch = url.match(/[?&]v=([^&]+)/);
    if (youtubeMatch) return youtubeMatch[1];

    // YouTube short URL format
    const shortMatch = url.match(/youtu\.be\/([^?&]+)/);
    if (shortMatch) return shortMatch[1];

    // Other platforms - try to extract from URL path
    const pathMatch = url.match(/\/([a-zA-Z0-9_-]+)(?:\?|$)/);
    if (pathMatch) return pathMatch[1];

    return 'unknown';
}

/**
 * Format duration from seconds to MM:SS or HH:MM:SS
 * @param {number} seconds - Duration in seconds
 * @returns {string} Formatted duration
 */
function formatDuration(seconds) {
    if (!seconds || isNaN(seconds)) return 'Unknown';

    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * Check if streaming is available for a URL using modern yt-dlp
 * @param {string} url - URL to check
 * @returns {Promise<boolean>} Whether streaming is available
 */
async function canStream(url) {
    try {
        // Use modern --print method to check if URL is valid
        const command = buildYtDlpCommand('--print "%(id)s" --no-download', url);
        const result = await executeYtDlpCommand(command, 8000, 1);
        return result && result.trim() && result.trim() !== 'NA';
    } catch (error) {
        console.warn('Streaming not available for URL:', url);
        return false;
    } finally {
        // Clean up temporary cookies file if it exists
        const cookiesFile = path.join(__dirname, '..', 'temp_cookies.txt');
        if (fs.existsSync(cookiesFile)) {
            try {
                fs.unlinkSync(cookiesFile);
            } catch (cleanupError) {
                // Ignore cleanup errors
            }
        }
    }
}



/**
 * Available audio filters for streaming
 */
const STREAMING_FILTERS = {
    'bass': 'bass=g=10',
    'treble': 'treble=g=5',
    'nightcore': 'asetrate=48000*1.25,aresample=48000,atempo=1.25',
    'vaporwave': 'asetrate=48000*0.8,aresample=48000,atempo=0.8',
    'echo': 'aecho=0.8:0.9:1000:0.3',
    'reverb': 'afreqshift=shift=0,aecho=0.8:0.88:60:0.4',
    'distortion': 'overdrive=20:20',
    'robot': 'afftfilt=real=\'hypot(re,im)*sin(0)\':imag=\'hypot(re,im)*cos(0)\':win_size=512:overlap=0.75',
    'chipmunk': 'asetrate=48000*1.5,aresample=48000',
    'deep': 'asetrate=48000*0.7,aresample=48000'
};

/**
 * Create streaming resource with filters applied
 * @param {string} streamingUrl - Direct streaming URL
 * @param {Array<string>} filterNames - Array of filter names to apply
 * @param {object} options - Additional options
 * @returns {AudioResource} Discord audio resource with filters
 */
function createStreamingResourceWithFilters(streamingUrl, filterNames = [], options = {}) {
    const filters = filterNames
        .filter(name => STREAMING_FILTERS[name])
        .map(name => STREAMING_FILTERS[name]);

    return createStreamingResource(streamingUrl, {
        ...options,
        filters: filters
    });
}









module.exports = {
    getStreamingUrl,
    createStreamingResource,
    createAudioResourceWithFallback,
    getSongInfoWithStreaming,
    formatDuration,
    canStream,
    createStreamingResourceWithFilters,
    STREAMING_FILTERS
};
