const axios = require('axios');
const { MessageAttachment } = require('discord.js');
module.exports = {
    name: 'gpti',
    aliases: ['generateimage', 'gimage'],
    category: 'utility',
    description: 'Menggunakan G4F untuk membuat gambar berdasarkan prompt',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        if (!args.length) {
            return message.reply('Kirimkan prompt untuk membuat gambar: `!gpti <prompt>`');
        }

        const prompt = args.join(' ');

        try {
            await message.channel.send('⏳ Sedang membuat gambar, mohon tunggu...');

            // <PERSON><PERSON> permintaan ke Python API
            const response = await axios.post('http://localhost:5000/image', { prompt });
            const imageUrl = response.data.url;

            // Kirim gambar ke pengguna
            await message.reply({ content:imageUrl });
        } catch (error) {
            console.error('Error generating image:', error.message);
            await message.reply('<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat membuat gambar. Coba lagi nanti.');
        }
    }
};
