const {
    ContextMenuCommandBuilder,
    ApplicationCommandType,
    MessageFlags,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
} = require('discord.js');

module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('MoCkInG TeXt')
        .setType(ApplicationCommandType.Message),

    async execute(client, interaction) {
        const message = interaction.targetMessage;

        if (!message || !message.content) {
            return interaction.reply({
                content: '❌ No text content to mock!',
                flags: MessageFlags.Ephemeral,
            });
        }

        const mockingText = message.content
            .split('')
            .map(char =>
                /[a-zA-Z]/.test(char)
                    ? Math.random() < 0.5
                        ? char.toLowerCase()
                        : char.toUpperCase()
                    : char
            )
            .join('');

        const copyButton = new ButtonBuilder()
            .setCustomId('copy_text')
            .setLabel('📋 Copy Text')
            .setStyle(ButtonStyle.Secondary);

        const row = new ActionRowBuilder().addComponents(copyButton);

        await interaction.reply({
            content: `🗿 **MoCkEd VeRsIoN:**\n${mockingText}`,
            components: [row],
            flags: MessageFlags.Ephemeral,
        });

        const collector = interaction.channel.createMessageComponentCollector({
            filter: i => i.customId === 'copy_text' && i.user.id === interaction.user.id,
            time: 60_000,
        });

        collector.on('collect', async i => {
            await i.reply({
                content: '✅ Copied! (Well, kinda — just select and copy it)',
                flags: MessageFlags.Ephemeral,
            });
        });
    },
};
