

const { QuickDB } = require('quick.db');

// Database configuration with optimizations
const dbConfig = {
    timeout: 10000, // 10 second timeout
    filePath: undefined // Will be set per database
};

const levelDB = new QuickDB({ ...dbConfig, filePath: 'database/levelDB.sqlite' });
const customCommandDB = new QuickDB({ ...dbConfig, filePath: 'database/customCommandsDB.sqlite' });
const rikaDB = new QuickDB({ ...dbConfig, filePath: 'database/rikaDB.sqlite' });
const holoDB = new QuickDB({ ...dbConfig, filePath: 'database/holoDB.sqlite' });
const snipeDB = new QuickDB({ ...dbConfig, filePath: 'database/snipeDB.sqlite' });
const gameDB = new QuickDB({ ...dbConfig, filePath: 'database/gameDB.sqlite' });

// Database cleanup utilities
const dbCleanup = {
    /**
     * Clean up old snipe data to prevent memory bloat
     */
    async cleanupSnipeData() {
        try {
            const allKeys = await snipeDB.all();
            const now = Date.now();
            const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days
            let cleanedCount = 0;

            for (const { id, value } of allKeys) {
                if (Array.isArray(value)) {
                    // Filter out old entries
                    const filteredLogs = value.filter(log => {
                        const logTime = new Date(log.deletedAt).getTime();
                        return now - logTime < maxAge;
                    });

                    if (filteredLogs.length !== value.length) {
                        if (filteredLogs.length === 0) {
                            await snipeDB.delete(id);
                        } else {
                            await snipeDB.set(id, filteredLogs);
                        }
                        cleanedCount++;
                    }
                }
            }

            if (cleanedCount > 0) {
                console.log(`🧹 Cleaned up ${cleanedCount} old snipe entries`);
            }
        } catch (error) {
            console.error('❌ Error cleaning up snipe data:', error);
        }
    },

    /**
     * Clean up old chat history to prevent memory bloat
     */
    async cleanupChatHistory() {
        try {
            const allKeys = await rikaDB.all();
            const maxHistorySize = 100; // Limit chat history size
            let cleanedCount = 0;

            for (const { id, value } of allKeys) {
                if (id.startsWith('chatHistory_') && Array.isArray(value)) {
                    if (value.length > maxHistorySize) {
                        // Keep only the most recent messages
                        const trimmedHistory = value.slice(-maxHistorySize);
                        await rikaDB.set(id, trimmedHistory);
                        cleanedCount++;
                    }
                }
            }

            if (cleanedCount > 0) {
                console.log(`🧹 Cleaned up ${cleanedCount} chat history entries`);
            }
        } catch (error) {
            console.error('❌ Error cleaning up chat history:', error);
        }
    },

    /**
     * Run all cleanup operations
     */
    async runAllCleanups() {
        console.log('🧹 Starting database cleanup...');
        await Promise.all([
            this.cleanupSnipeData(),
            this.cleanupChatHistory()
        ]);
        console.log('✅ Database cleanup completed');
    }
};

// Start periodic cleanup (every 6 hours)
const cleanupInterval = setInterval(() => {
    dbCleanup.runAllCleanups();
}, 6 * 60 * 60 * 1000);

// Track interval for cleanup
if (global.client && global.client.activeIntervals) {
    global.client.activeIntervals.add(cleanupInterval);
}

// Export all initialized databases and utilities
module.exports = {
    levelDB,
    customCommandDB,
    rikaDB,
    holoDB,
    snipeDB,
    gameDB,
    dbCleanup,
    // Add other exported DBs here
};
