

const { QuickDB } = require('quick.db');

const levelDB = new QuickDB({ filePath: 'database/levelDB.sqlite' });
const customCommandDB = new QuickDB({ filePath: 'database/customCommandsDB.sqlite' });// Add more databases as needed
const rikaDB = new QuickDB({ filePath: 'database/rikaDB.sqlite' });
const holoDB = new QuickDB({ filePath: 'database/holoDB.sqlite' });
const snipeDB = new QuickDB({ filePath: 'database/snipeDB.sqlite' });
const gameDB = new QuickDB({ filePath: 'database/gameDB.sqlite' });
// Export all initialized databases in a single object
module.exports = {
    levelDB,
    customCommandDB,
    rikaDB,
    holoDB,
    snipeDB,
    gameDB,
    // Add other exported DBs here
};
