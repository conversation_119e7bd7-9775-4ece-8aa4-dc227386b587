const fs = require('fs');

const {
    createThemedEmbed,
    formatSongTitle,
    createStatusIndicator,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

function deleteFile(filePath, retries = 3, delay = 1000) {
    if (!filePath || !fs.existsSync(filePath)) return;

    const attemptDelete = (attempt) => {
        fs.unlink(filePath, (err) => {
            if (err) {
                if (err.code === 'EBUSY' && attempt < retries) {
                    // File is busy, retry after delay
                    console.log(`File ${filePath} is busy, retrying in ${delay}ms... (attempt ${attempt + 1}/${retries})`);
                    setTimeout(() => attemptDelete(attempt + 1), delay);
                } else if (err.code === 'ENOENT') {
                    // File doesn't exist anymore, that's fine
                    return;
                } else {
                    console.error(`Error deleting file ${filePath} after ${attempt} attempts:`, err.message);
                }
            }
        });
    };

    attemptDelete(1);
}

module.exports = {
    name: 'clear',
    description: 'Clear all songs from the queue (except currently playing)',
    aliases: ['cl', 'empty'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || serverQueue.songs.length === 0) {
            const emptyQueueEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Empty Queue`)
                .setDescription(`${createStatusIndicator('error', 'The music queue is already empty!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to add music to the queue.`);
            return message.reply({ embeds: [emptyQueueEmbed] });
        }

        // If only one song (currently playing), nothing to clear
        if (serverQueue.songs.length === 1) {
            const onlyCurrentEmbed = createThemedEmbed('warning')
                .setTitle(`${EMOJIS.WARNING} Only Current Song`)
                .setDescription(`${createStatusIndicator('warning', 'Only the currently playing song is in the queue')}\n\n${EMOJIS.INFO} Nothing to clear!`);
            return message.reply({ embeds: [onlyCurrentEmbed] });
        }

        const songsToRemove = serverQueue.songs.length - 1; // All except currently playing

        // Clean up files for songs that will be removed (all except the first one)
        for (let i = 1; i < serverQueue.songs.length; i++) {
            const song = serverQueue.songs[i];
            if (song.filePath) {
                deleteFile(song.filePath);
            }
        }

        // Keep only the currently playing song
        serverQueue.songs = serverQueue.songs.slice(0, 1);

        const clearEmbed = createThemedEmbed('success')
            .setTitle(`${EMOJIS.SUCCESS} Queue Cleared`)
            .setDescription(`${createStatusIndicator('success', `Cleared ${songsToRemove} song${songsToRemove !== 1 ? 's' : ''} from the queue`)}`)
            .addFields({
                name: `${EMOJIS.PLAY} Currently Playing`,
                value: serverQueue.currentSong ?
                    formatSongTitle(serverQueue.currentSong.title, serverQueue.currentSong.displayUrl || serverQueue.currentSong.url) :
                    'None',
                inline: false
            })
            .setFooter({ text: `${EMOJIS.INFO} The currently playing song was not affected.` });

        message.reply({ embeds: [clearEmbed] });
    },
};
