# discord-bot-templates
Yet another Discord Bot Template 

## ✅ Features
- [x] Text and Slash based Command handling
- [x] Individual interaction components handling
- [x] commands examples
- [x] **Bonus**: Music commands

## 🗒️ Requirements
- [Node.js](https://nodejs.org/en/download) 22 or higher
- [FFmpeg](https://ffmpeg.org) for music commands to work

## 🛠️ Setup
- Create a [Discord](https://discord.com) account 
- Create a bot [Here](https://discord.com/developers/applications)
- Go to your application -> General Information then copy save the 'Application ID'
- Go to your application -> Bot then click reset token/issue token and save it
- In your project folder, create a `.env` file and fill with your Application ID and Token   

  ```bash
   token=    # your token
   clientId= # your clientId
   ```
- Start the bot
  
    ```bash
    node .
    ```
