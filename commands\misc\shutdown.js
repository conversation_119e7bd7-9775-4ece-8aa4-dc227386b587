const { exec } = require("child_process");

module.exports = {
    name: "shutdown",
    aliases: ["stdn", "sd", "poweroff"],
    category: "misc",
    description: "Shut down the bot",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        // Periksa apakah pengguna memiliki izin
        if (message.author.id !== process.env.tama) {
            return message.reply("You don't have the power to shut me down!");
        }

        // Kirim pesan konfirmasi sebelum shutdown
        await message.channel.send("Shutting down... Goodbye!");

        // Platform-specific shutdown command
        const shutdownCommand = process.platform === "win32"
            ? 'taskkill /f /im node.exe'
            : `kill -9 ${process.pid}`;

        // Eksekusi perintah untuk menghentikan proses Node.js
        exec(shutdownCommand, (error, stdout, stderr) => {
            if (error) {
                console.error(`Error during shutdown: ${error.message}`);
                return message.channel.send("Failed to shut down the bot. Please try again later.");
            }
            if (stderr) console.error(`Stderr: ${stderr}`);
            console.log(`Stdout: ${stdout}`);
            process.exit(0); // Hentikan proses utama
        });
    },
};
