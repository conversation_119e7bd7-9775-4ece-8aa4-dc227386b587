const { rikaDB } = require("../../database/manager");
const axios = require("axios");

const AI_CHANNEL_ID = process.env.channelId;

module.exports = async (client, message) => {
    if (message.author.bot || message.channel.id !== AI_CHANNEL_ID) return;

    const userId = message.author.id;
    const userName = message.author.username;
    const guildName = message.guild ? message.guild.name : "Direct Message";

    let chatHistory = (await rikaDB.get(`chatHistory_${AI_CHANNEL_ID}`)) || [];

    if (chatHistory.length >= 50) chatHistory.shift();

    // If there's an image attachment
    if (message.attachments.size > 0) {
        const imageAttachment = message.attachments.find(att => att.contentType && att.contentType.startsWith("image/"));
        if (imageAttachment) {
            try {
                await message.channel.sendTyping();

                // User-defined question or default
                const userQuestion = message.content.trim() || "Can you describe what's in this image?";

                const response = await axios.post("http://localhost:5000/analyze_image", {
                    image_url: imageAttachment.url,
                    question: userQuestion
                });

                const description = response.data.answer;

                // Save to chat history
                chatHistory.push({
                    role: "user",
                    name: userName,
                    content: `${userQuestion} (Image: ${imageAttachment.url})`
                });

                chatHistory.push({
                    role: "assistant",
                    content: `${description}`
                });

                await rikaDB.set(`chatHistory_${AI_CHANNEL_ID}`, chatHistory);

                await message.reply(`${description}`);
                return;
            } catch (error) {
                console.error("Image analysis error:", error.message);
                await message.reply("Hmm… I had trouble analyzing that image. Try again later!");
                return;
            }
        }
    }

    // If it's not an image, treat it as a text message
    const userMessage = message.content.trim();
    if (!userMessage) return;

    chatHistory.push({
        role: "user",
        name: userName,
        content: userMessage,
    });

    try {
        await message.channel.sendTyping();

        const usersInChat = [...new Set(chatHistory.filter(m => m.role === "user").map(m => m.name))].join(", ") || "No one yet";
        const systemMessage = {
            role: "system",
            content: `
            Intro: you're a bot named Rikai 理解 created by tama_0612 for this discord server.
            You have a sister named Baquabot, also from the same creator; she is a LINE bot.

            Task:
            - Engage in a natural conversation with multiple users.
            - Recognize and remember users actively chatting.
            - Provide reliable information sources when necessary.
            - When asked about commands, tell them to type "!help".
            - Occasionally add humor or light sarcasm.
            - Avoid replying in Spanish unless initiated by a user.
            - Keep responses brief and engaging.

            Current Chat Room:
            - Server: ${guildName}
            - Active Participants: ${usersInChat}
            - Is Chatting: ${userName}
            `
        };

        const response = await axios.post("http://localhost:5000/chat", {
            messages: [systemMessage, ...chatHistory]
        });

        const botReply = response.data;

        chatHistory.push({
            role: "assistant",
            content: botReply,
        });

        await rikaDB.set(`chatHistory_${AI_CHANNEL_ID}`, chatHistory);

        await message.reply(`${botReply}`);
    } catch (error) {
        console.error("Error contacting AI:", error.message);
        await message.reply("😴 I'm currently taking a little nap. Check back later when I'm awake!");
    }
};
