/**
 * Converts a timestamp into a "time ago" format (e.g., "5 minutes ago").
 *
 * @param {Date | number} timestamp - The date or timestamp to format.
 * @returns {string} - A human-readable time difference.
 */
function timeAgo(timestamp) {
    const now = new Date();
    const time = typeof timestamp === "number" ? new Date(timestamp) : timestamp;
    const seconds = Math.floor((now - time) / 1000);

    const intervals = {
        year: 31536000,
        month: 2592000,
        week: 604800,
        day: 86400,
        hour: 3600,
        minute: 60,
        second: 1,
    };

    for (const [unit, value] of Object.entries(intervals)) {
        const count = Math.floor(seconds / value);
        if (count > 0) return `${count} ${unit}${count !== 1 ? 's' : ''} ago`;
    }

    return "just now";
}

module.exports = { timeAgo };
