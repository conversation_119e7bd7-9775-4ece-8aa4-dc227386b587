const fs = require('fs');
const path = require('path');

const wordlist = fs.readFileSync(path.join(__dirname, '../../utils/indonesian-wordlist.txt'), 'utf8')
  .split('\n')
  .map(w => w.trim().toLowerCase())
  .filter(w => w.length > 0);

function getRandomSubstring(word) {
  if (word.length <= 3) return word;
  const start = Math.floor(Math.random() * (word.length - 2));
  return word.substring(start, start + 3);
}
function wait(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

function getRandomWord() {
  return wordlist[Math.floor(Math.random() * wordlist.length)];
}

module.exports = {
  name: 'wordguess',
  aliases: [],
  category: 'game',
  description: 'Tebak kata berdasarkan substring acak!',
  usePrefix: true,
  isEverywhere: false,

  async execute(message, args) {
    await message.channel.send('Memulai Word Guess...');
    await wait(2000);
    let score = 0;
    let health = Math.min(parseInt(args[0]) || 3, 7);
    const timeoutSeconds = Math.min(parseInt(args[1]) || 30, 280);
    const timeout = timeoutSeconds * 1000;
    const usedWords = new Set();
    let stopped = false;
    let stopCount = false;

    while (health > 0 && !stopped) {
      const targetWord = getRandomWord();
      const substring = getRandomSubstring(targetWord);

      const botMessage = await message.channel.send(`Ketik sebuah kata yang mengandung **${substring}**`);

      const filter = m => m.author.id === message.author.id;
      const collector = message.channel.createMessageCollector({ filter, time: timeout });

      let answered = false;
      let timeoutHandle;

      const countdownEmojis = ['3️⃣', '2️⃣', '1️⃣'];
      timeoutHandle = setTimeout(async () => {
        for (let i = 0; i < countdownEmojis.length; i++) {
          setTimeout(() => {
            if (!stopCount) { // <-- cek dulu
              botMessage.react(countdownEmojis[i]).catch(() => {});
            }
          }, (i + 1) * 1000);
        }
      }, timeout - 3000);
      

      collector.on('collect', async m => {
        const input = m.content.toLowerCase().trim();
        
        if (input === 'stop') {
          stopped = true;
          collector.stop();
          await m.react('🛑');
          return;
        }

        if (!wordlist.includes(input)) {
          await m.react('⚠️'); // Kata tidak ada di wordlist
          return;
        }

        if (!input.includes(substring)) {
          await m.react('⚠️'); // Tidak mengandung substring
          return;
        }

        if (usedWords.has(input)) {
          await m.react('❌'); // Sudah pernah digunakan
          return;
        }

        // Jawaban benar
        answered = true;
        stopCount = true;
        usedWords.add(input);
        score++;

        await m.react('✅');
        collector.stop();
        await wait(1000);
      });

      await new Promise(resolve => {
        collector.on('end', async collected => {
          clearTimeout(timeoutHandle);
          
          if (!answered && !stopped) {
            await wait(2000)
            health--;
            await message.channel.send(`⏰ Waktu habis! -1 health 💖 (tersisa ${health})`);
          }
          resolve();
        });
      });
    }

    await message.channel.send(`💀 Game berakhir! Skor akhir kamu: **${score}**. Terima kasih sudah bermain!`);
  }
};
