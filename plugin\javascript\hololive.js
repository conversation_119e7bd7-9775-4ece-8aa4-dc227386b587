const {holoDB} = require("../../database/manager")
const { EmbedBuilder } = require("discord.js");
const axios = require("axios");

const HOLODEX_API_KEY = process.env.HOLODEX_API_KEY;

const channelIds = [
  "UCrV1Hf5r8P148idjoSfrGEQ", // Replace with VTuber's channel IDs
  "UC1DCedRgGHBdm81E1llLhOQ",
  "UC6eWCld0KwmyHFbAqK3V-Rw"
];

function delay(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

async function getChannelName(channelId) {
  try {
    const res = await axios.get(`https://holodex.net/api/v2/channels/${channelId}`, {
      headers: { "X-APIKEY": HOLODEX_API_KEY }
    });
    return res.data.name;
  } catch (err) {
    console.error("Failed to fetch channel name:", err.message);
    return null;
  }
}

function createEmbed({ type, title, videoId, thumbnail, channelName, scheduleTime }) {
  const url = `https://www.youtube.com/watch?v=${videoId}`;
  const embed = new EmbedBuilder()
    .setTitle(title)
    .setURL(url)
    .setThumbnail(thumbnail)
    .setColor(type === "live" ? 0xFF0000 : type === "upcoming" ? 0xFFA500 : 0x00BFFF)
    .setFooter({ text: channelName });

  if (type === "live") {
    embed.setDescription(`${channelName} is **currently live**!`);
  } else if (type === "upcoming") {
    embed.setDescription(`${channelName} has an **upcoming stream**.`);
    if (scheduleTime) {
      embed.addFields({ name: "Scheduled at", value: `<t:${Math.floor(new Date(scheduleTime).getTime() / 1000)}:F>`, inline: false });
    }
  } else {
    embed.setDescription(`${channelName} just **uploaded a new video**.`);
  }

  return embed;
}

async function checkYouTubeStatus(client) {
  const notifyChannel = client.channels.cache.get(process.env.botMainChannel);
  if (!notifyChannel) return console.warn("Notification channel not found.");

  for (const channelId of channelIds) {
    const channelName = await getChannelName(channelId);
    if (!channelName) continue;

    await delay(1000);

    try {
      const [videoRes, liveRes] = await Promise.all([
        axios.get(`https://holodex.net/api/v2/channels/${channelId}/videos`, {
          headers: { "X-APIKEY": HOLODEX_API_KEY },
          params: { limit: 1, type: "videos", include: "clips", order: "newest" }
        }),
        axios.get(`https://holodex.net/api/v2/live`, {
          headers: { "X-APIKEY": HOLODEX_API_KEY },
          params: { channel_id: channelId, status: "live,upcoming" }
        })
      ]);

      const latestVideo = videoRes.data[0];
      const liveVideos = liveRes.data.filter(v => v.status === "live");
      const upcomingVideos = liveRes.data.filter(v => v.status === "upcoming");

      const liveVideo = liveVideos[0];
      const upcomingVideo = upcomingVideos[0];

      const lastVideoId = await holoDB.get(`${channelId}_lastVideo`);
      const liveNotified = await holoDB.get(`${channelId}_liveNotified`);
      const upcomingNotified = await holoDB.get(`${channelId}_upcomingNotified`);

      // New Video Upload
      if (latestVideo && latestVideo.id !== lastVideoId) {
        const embed = createEmbed({
          type: "upload",
          title: latestVideo.title,
          videoId: latestVideo.id,
          thumbnail: `https://i.ytimg.com/vi/${latestVideo.id}/hqdefault.jpg`,
          channelName
        });
        await notifyChannel.send({ embeds: [embed] });
        await holoDB.set(`${channelId}_lastVideo`, latestVideo.id);
        console.log(`[✔] New video alert for ${channelName}`);
      }

      // Live Stream
      if (liveVideo && !liveNotified) {
        const embed = createEmbed({
          type: "live",
          title: liveVideo.title,
          videoId: liveVideo.id,
          thumbnail: liveVideo.thumbnail || `https://i.ytimg.com/vi/${liveVideo.id}/hqdefault.jpg`,
          channelName
        });
        await notifyChannel.send({ embeds: [embed] });
        await holoDB.set(`${channelId}_liveNotified`, true);
        console.log(`[✔] Live alert sent for ${channelName}`);
      } else if (!liveVideo && liveNotified) {
        await holoDB.set(`${channelId}_liveNotified`, false);
      }

      // Upcoming Stream
      if (upcomingVideo && !upcomingNotified) {
        const embed = createEmbed({
          type: "upcoming",
          title: upcomingVideo.title,
          videoId: upcomingVideo.id,
          thumbnail: upcomingVideo.thumbnail || `https://i.ytimg.com/vi/${upcomingVideo.id}/hqdefault.jpg`,
          channelName,
          scheduleTime: upcomingVideo.available_at
        });
        await notifyChannel.send({ embeds: [embed] });
        await holoDB.set(`${channelId}_upcomingNotified`, true);
        console.log(`[✔] Upcoming stream alert sent for ${channelName}`);
      } else if (!upcomingVideo && upcomingNotified) {
        await holoDB.set(`${channelId}_upcomingNotified`, false);
      }

    } catch (err) {
      if (err.response?.status === 429) {
        console.error(`Rate limit hit for ${channelId}. Try again later.`);
      } else {
        console.error(`Error fetching data for ${channelId}:`, err.message);
      }
    }

    await delay(1000);
  }
}

module.exports = { checkYouTubeStatus };
