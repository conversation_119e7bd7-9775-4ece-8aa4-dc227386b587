const { EmbedBuilder } = require('discord.js');
const { networkMonitor } = require('../../utils/networkStability');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

module.exports = {
    name: 'diagnostic',
    description: 'Show network and audio stability diagnostics',
    aliases: ['diag', 'stability', 'status'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);
        
        // Create network stability report
        const networkEmbed = networkMonitor.createDiagnosticEmbed(message.guild.id);
        
        // Add current music status
        if (serverQueue && serverQueue.currentSong) {
            networkEmbed.addFields({
                name: '🎵 Current Status',
                value: `Playing: **${serverQueue.currentSong.title}**\nQueue: ${serverQueue.songs.length} song${serverQueue.songs.length !== 1 ? 's' : ''}`,
                inline: false
            });
        } else {
            networkEmbed.addFields({
                name: '🎵 Current Status',
                value: 'No music currently playing',
                inline: false
            });
        }

        // Add bot performance info
        const memoryUsage = process.memoryUsage();
        const uptime = process.uptime();
        
        const performanceInfo = [
            `**Uptime:** ${Math.floor(uptime / 3600)}h ${Math.floor((uptime % 3600) / 60)}m`,
            `**Memory:** ${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB used`,
            `**CPU:** ${process.cpuUsage().user / 1000}ms user time`
        ].join('\n');

        networkEmbed.addFields({
            name: '⚙️ Bot Performance',
            value: performanceInfo,
            inline: false
        });

        // Add troubleshooting tips
        const troubleshootingTips = [
            '• If experiencing audio issues, try `!stop` and `!play` again',
            '• Check your internet connection stability',
            '• Ensure the bot has proper voice permissions',
            '• Try using a different audio quality with yt-dlp'
        ].join('\n');

        networkEmbed.addFields({
            name: '🔧 Troubleshooting Tips',
            value: troubleshootingTips,
            inline: false
        });

        // Add reset option
        if (args.includes('reset') || args.includes('clear')) {
            networkMonitor.resetGuildStats(message.guild.id);
            networkEmbed.setFooter({ text: 'Network statistics have been reset for this server.' });
        } else {
            networkEmbed.setFooter({ text: 'Use "diagnostic reset" to clear statistics.' });
        }

        message.reply({ embeds: [networkEmbed] });
    },
};
