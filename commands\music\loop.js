const {
    createThemedEmbed,
    createStatusIndicator,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

module.exports = {
    name: 'loop',
    description: 'Toggle loop mode for the current song or queue',
    aliases: ['repeat', 'l'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || serverQueue.songs.length === 0) {
            const emptyQueueEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Music`)
                .setDescription(`${createStatusIndicator('error', 'There is no music in the queue!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to add music to the queue.`);
            return message.reply({ embeds: [emptyQueueEmbed] });
        }

        // Initialize loop mode if it doesn't exist
        if (!serverQueue.hasOwnProperty('loopMode')) {
            serverQueue.loopMode = 'off'; // 'off', 'song', 'queue'
        }

        let newLoopMode = 'off';

        // Parse the argument for loop mode
        if (args.length > 0) {
            const mode = args[0].toLowerCase();
            switch (mode) {
                case 'song':
                case 'track':
                case 'current':
                case '1':
                    newLoopMode = 'song';
                    break;
                case 'queue':
                case 'all':
                case 'playlist':
                case '2':
                    newLoopMode = 'queue';
                    break;
                case 'off':
                case 'none':
                case 'disable':
                case '0':
                    newLoopMode = 'off';
                    break;
                default:
                    // If invalid argument, show help
                    const helpEmbed = createThemedEmbed('info')
                        .setTitle(`${EMOJIS.REPEAT} Loop Command Help`)
                        .setDescription(`${createStatusIndicator('info', 'Loop mode options')}\n\n**Usage:** \`loop [mode]\``)
                        .addFields({
                            name: `${EMOJIS.INFO} Available Modes`,
                            value: `${EMOJIS.REPEAT_ONE} \`song\` - Loop current song\n${EMOJIS.REPEAT} \`queue\` - Loop entire queue\n${EMOJIS.STOP} \`off\` - Disable loop\n\n${EMOJIS.SPARKLES} If no mode is specified, it will cycle through the modes.`,
                            inline: false
                        });
                    return message.reply({ embeds: [helpEmbed] });
            }
        } else {
            // No argument provided, cycle through modes
            switch (serverQueue.loopMode) {
                case 'off':
                    newLoopMode = 'song';
                    break;
                case 'song':
                    newLoopMode = 'queue';
                    break;
                case 'queue':
                    newLoopMode = 'off';
                    break;
            }
        }

        serverQueue.loopMode = newLoopMode;

        // Create embed based on the new loop mode
        let title, description, color, emoji;

        switch (newLoopMode) {
            case 'song':
                title = '🔂 Loop: Current Song';
                description = 'The current song will repeat when it ends.';
                color = '#00FF00';
                emoji = '🔂';
                break;
            case 'queue':
                title = '🔁 Loop: Queue';
                description = 'The entire queue will repeat when it reaches the end.';
                color = '#0099FF';
                emoji = '🔁';
                break;
            case 'off':
                title = '▶️ Loop: Disabled';
                description = 'Loop mode has been turned off.';
                color = '#FF0000';
                emoji = '▶️';
                break;
        }

        // Determine embed type based on loop mode
        let embedType = 'success';
        if (newLoopMode === 'off') embedType = 'warning';

        const loopEmbed = createThemedEmbed(embedType)
            .setTitle(`${emoji} ${title}`)
            .setDescription(`${createStatusIndicator(embedType === 'warning' ? 'warning' : 'success', description)}`);

        if (serverQueue.currentSong) {
            loopEmbed.addFields({
                name: `${EMOJIS.PLAY} Currently Playing`,
                value: `${EMOJIS.MUSIC_NOTE} [${serverQueue.currentSong.title}](${serverQueue.currentSong.displayUrl || serverQueue.currentSong.url})`,
                inline: false
            });
        }

        loopEmbed.setFooter({
            text: `${EMOJIS.QUEUE} Queue: ${serverQueue.songs.length} song${serverQueue.songs.length !== 1 ? 's' : ''} | ${EMOJIS.REPEAT} Use 'loop' to cycle modes`
        });

        message.reply({ embeds: [loopEmbed] });
    },
};
