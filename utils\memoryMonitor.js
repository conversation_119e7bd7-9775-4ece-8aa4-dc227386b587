const { EmbedBuilder } = require('discord.js');

/**
 * Memory monitoring and optimization utility
 */
class MemoryMonitor {
    constructor() {
        this.startTime = Date.now();
        this.lastGC = Date.now();
        this.memoryHistory = [];
        this.maxHistorySize = 100;
        this.warningThreshold = 500 * 1024 * 1024; // 500MB
        this.criticalThreshold = 1024 * 1024 * 1024; // 1GB
        this.monitoringInterval = null;
        this.isMonitoring = false;
    }

    /**
     * Start memory monitoring
     * @param {number} intervalMs - Monitoring interval in milliseconds (default: 30 seconds)
     */
    startMonitoring(intervalMs = 30000) {
        if (this.isMonitoring) {
            console.warn('⚠️ Memory monitoring is already running');
            return;
        }

        this.monitoringInterval = setInterval(() => {
            this.checkMemoryUsage();
        }, intervalMs);

        // Track interval for cleanup
        if (global.client && global.client.activeIntervals) {
            global.client.activeIntervals.add(this.monitoringInterval);
        }

        this.isMonitoring = true;
        console.log('🔍 Memory monitoring started');
    }

    /**
     * Stop memory monitoring
     */
    stopMonitoring() {
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            
            // Remove from tracking
            if (global.client && global.client.activeIntervals) {
                global.client.activeIntervals.delete(this.monitoringInterval);
            }
            
            this.monitoringInterval = null;
        }
        
        this.isMonitoring = false;
        console.log('⏹️ Memory monitoring stopped');
    }

    /**
     * Check current memory usage and take action if needed
     */
    checkMemoryUsage() {
        const memUsage = process.memoryUsage();
        const timestamp = Date.now();

        // Add to history
        this.memoryHistory.push({
            timestamp,
            rss: memUsage.rss,
            heapUsed: memUsage.heapUsed,
            heapTotal: memUsage.heapTotal,
            external: memUsage.external
        });

        // Limit history size
        if (this.memoryHistory.length > this.maxHistorySize) {
            this.memoryHistory.shift();
        }

        // Check thresholds
        if (memUsage.heapUsed > this.criticalThreshold) {
            console.warn('🚨 CRITICAL: Memory usage is very high!', this.formatBytes(memUsage.heapUsed));
            this.forceGarbageCollection();
            this.cleanupResources();
        } else if (memUsage.heapUsed > this.warningThreshold) {
            console.warn('⚠️ WARNING: Memory usage is high', this.formatBytes(memUsage.heapUsed));
            this.optimizeMemory();
        }

        // Log memory stats every 10 minutes
        if (timestamp - this.lastGC > 600000) { // 10 minutes
            console.log('📊 Memory Stats:', {
                heap: this.formatBytes(memUsage.heapUsed),
                rss: this.formatBytes(memUsage.rss),
                uptime: this.formatUptime(timestamp - this.startTime)
            });
            this.lastGC = timestamp;
        }
    }

    /**
     * Force garbage collection and cleanup
     */
    forceGarbageCollection() {
        try {
            if (global.gc) {
                global.gc();
                console.log('🧹 Forced garbage collection completed');
            } else {
                console.warn('⚠️ Garbage collection not available (run with --expose-gc)');
            }
        } catch (error) {
            console.error('❌ Error during garbage collection:', error);
        }
    }

    /**
     * Optimize memory usage
     */
    optimizeMemory() {
        try {
            // Clear old memory history
            if (this.memoryHistory.length > 50) {
                this.memoryHistory = this.memoryHistory.slice(-25);
            }

            // Run garbage collection if available
            if (global.gc) {
                global.gc();
            }

            console.log('🔧 Memory optimization completed');
        } catch (error) {
            console.error('❌ Error during memory optimization:', error);
        }
    }

    /**
     * Clean up various resources
     */
    cleanupResources() {
        try {
            // Clean up temporary files
            const fs = require('fs');
            const path = require('path');
            
            const tempDirs = [
                path.join(process.cwd(), 'web', 'temp'),
                path.join(process.cwd(), 'plugin', 'javascript', 'temp_images')
            ];

            tempDirs.forEach(tempDir => {
                if (fs.existsSync(tempDir)) {
                    const files = fs.readdirSync(tempDir);
                    let cleanedCount = 0;
                    
                    files.forEach(file => {
                        try {
                            const filePath = path.join(tempDir, file);
                            const stats = fs.statSync(filePath);
                            
                            // Delete files older than 1 hour
                            if (Date.now() - stats.mtime.getTime() > 3600000) {
                                fs.unlinkSync(filePath);
                                cleanedCount++;
                            }
                        } catch (error) {
                            // Ignore individual file errors
                        }
                    });
                    
                    if (cleanedCount > 0) {
                        console.log(`🧹 Cleaned ${cleanedCount} old temp files from ${tempDir}`);
                    }
                }
            });

            console.log('🧹 Resource cleanup completed');
        } catch (error) {
            console.error('❌ Error during resource cleanup:', error);
        }
    }

    /**
     * Get current memory statistics
     * @returns {object} Memory statistics
     */
    getMemoryStats() {
        const memUsage = process.memoryUsage();
        const uptime = Date.now() - this.startTime;

        return {
            current: {
                rss: memUsage.rss,
                heapUsed: memUsage.heapUsed,
                heapTotal: memUsage.heapTotal,
                external: memUsage.external
            },
            formatted: {
                rss: this.formatBytes(memUsage.rss),
                heapUsed: this.formatBytes(memUsage.heapUsed),
                heapTotal: this.formatBytes(memUsage.heapTotal),
                external: this.formatBytes(memUsage.external)
            },
            uptime: this.formatUptime(uptime),
            isMonitoring: this.isMonitoring,
            historySize: this.memoryHistory.length
        };
    }

    /**
     * Create memory status embed
     * @returns {EmbedBuilder} Memory status embed
     */
    createMemoryEmbed() {
        const stats = this.getMemoryStats();
        const memUsage = stats.current;

        let color = '#00FF00'; // Green
        let status = 'Healthy';

        if (memUsage.heapUsed > this.criticalThreshold) {
            color = '#FF0000'; // Red
            status = 'Critical';
        } else if (memUsage.heapUsed > this.warningThreshold) {
            color = '#FFA500'; // Orange
            status = 'Warning';
        }

        const embed = new EmbedBuilder()
            .setColor(color)
            .setTitle('🔍 Memory Monitor')
            .setDescription(`Status: **${status}**`)
            .addFields(
                { name: 'Heap Used', value: stats.formatted.heapUsed, inline: true },
                { name: 'Heap Total', value: stats.formatted.heapTotal, inline: true },
                { name: 'RSS', value: stats.formatted.rss, inline: true },
                { name: 'External', value: stats.formatted.external, inline: true },
                { name: 'Uptime', value: stats.uptime, inline: true },
                { name: 'Monitoring', value: stats.isMonitoring ? '✅ Active' : '❌ Inactive', inline: true }
            )
            .setTimestamp();

        return embed;
    }

    /**
     * Format bytes to human readable format
     * @param {number} bytes - Bytes to format
     * @returns {string} Formatted string
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * Format uptime to human readable format
     * @param {number} ms - Milliseconds to format
     * @returns {string} Formatted string
     */
    formatUptime(ms) {
        const seconds = Math.floor(ms / 1000);
        const minutes = Math.floor(seconds / 60);
        const hours = Math.floor(minutes / 60);
        const days = Math.floor(hours / 24);

        if (days > 0) return `${days}d ${hours % 24}h ${minutes % 60}m`;
        if (hours > 0) return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
        if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
        return `${seconds}s`;
    }
}

// Create singleton instance
const memoryMonitor = new MemoryMonitor();

module.exports = {
    MemoryMonitor,
    memoryMonitor
};
