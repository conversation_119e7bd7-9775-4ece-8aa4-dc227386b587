const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle, MessageFlags } = require('discord.js');
const { useQueue } = require('discord-player');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('queue')
        .setDescription('Displays the current song queue with pagination'),

    async execute(interaction) {
        const queue = useQueue(interaction.guild.id);

        if (!queue || (!queue.currentTrack && queue.tracks.size === 0)) {
            return interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ The queue is empty!')
                        .setColor('#FF0000') // Red color
                ],
                flags: MessageFlags.Ephemeral
            });
        }

        const tracksPerPage = 10;
        const trackArray = queue.tracks.toArray(); // Convert TrackQueue to an array

        // Create full queue array including current track
        const fullQueue = [];
        if (queue.currentTrack) {
            fullQueue.push(queue.currentTrack);
        }
        fullQueue.push(...trackArray);

        const pages = Math.ceil(fullQueue.length / tracksPerPage);
        let currentPage = 0;

        const generateEmbed = (page) => {
            const start = page * tracksPerPage;
            const end = start + tracksPerPage;
            const trackList = fullQueue
                .slice(start, end)
                .map((track, index) => {
                    const position = start + index + 1;
                    const isCurrentTrack = queue.currentTrack && track === queue.currentTrack;
                    const prefix = isCurrentTrack ? '🎵 **[NOW PLAYING]**' : `${position}.`;
                    return `${prefix} [${track.title}](${track.url})`;
                })
                .join('\n') || 'No songs in queue.';

            return new EmbedBuilder()
                .setTitle(`🎶 Current Queue (Page ${page + 1}/${pages})`)
                .setDescription(trackList)
                .setColor('#00FF00') // Green color
                .setThumbnail(interaction.guild.iconURL({ dynamic: true }) || null)
                .setFooter({ text: `Total songs: ${fullQueue.length} | Queued: ${trackArray.length}` });
        };

        const row = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('prev')
                .setLabel('⬅️')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage === 0),
            new ButtonBuilder()
                .setCustomId('next')
                .setLabel('➡️')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage === pages - 1)
        );

        const message = await interaction.reply({
            embeds: [generateEmbed(currentPage)],
            components: pages > 1 ? [row] : [], // Show buttons only if more than 1 page
            fetchReply: true
        });

        if (pages <= 1) return; // No need for pagination if only 1 page

        const collector = message.createMessageComponentCollector({ time: 60000 });

        collector.on('collect', async (buttonInteraction) => {
            if (buttonInteraction.user.id !== interaction.user.id) {
                return buttonInteraction.reply({
                    content: "❌ **You're not allowed to interact with this!**",
                    flags: MessageFlags.Ephemeral
                });
            }

            if (buttonInteraction.customId === 'prev') currentPage--;
            if (buttonInteraction.customId === 'next') currentPage++;

            await buttonInteraction.update({
                embeds: [generateEmbed(currentPage)],
                components: [
                    new ActionRowBuilder().addComponents(
                        new ButtonBuilder()
                            .setCustomId('prev')
                            .setLabel('⬅️')
                            .setStyle(ButtonStyle.Primary)
                            .setDisabled(currentPage === 0),
                        new ButtonBuilder()
                            .setCustomId('next')
                            .setLabel('➡️')
                            .setStyle(ButtonStyle.Primary)
                            .setDisabled(currentPage === pages - 1)
                    )
                ]
            });
        });

        collector.on('end', () => {
            interaction.editReply({ components: [] }).catch(() => {});
        });
    }
};
