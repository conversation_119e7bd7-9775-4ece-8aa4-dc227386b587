const axios = require("axios");
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");

module.exports = {
    name: "steam",
    aliases: [],
    category: "utility",
    description: "Search for games on the Steam Store.",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const query = args.join(" ");
        if (!query) {
            return message.reply("❌ Please provide a game title. Example: `!steam dota`");
        }

        try {
            // Fetch game data
            const response = await axios.get(`https://store.steampowered.com/api/storesearch/?cc=id&term=${encodeURIComponent(query)}`);
            const games = response.data.items;

            if (!games || games.length === 0) {
                return message.reply(`❌ No results found for **"${query}"**.`);
            }

            let currentIndex = 0;

            // Fetch and display initial game details
            const fetchGameDetails = async (index) => {
                const game = games[index];
                const gameId = game.id;

                const detailsRes = await axios.get(`https://store.steampowered.com/api/appdetails/?cc=id&appids=${gameId}`);
                const gameDetails = detailsRes.data[gameId]?.data;

                if (!gameDetails) return null;

                // Extract data
                const gameImage = gameDetails.header_image;
                const gameName = gameDetails.name || "No title available.";
                const description = gameDetails.short_description || "No description available.";
                const developer = gameDetails.developers?.join(", ") || "Unknown.";
                const publisher = gameDetails.publishers?.join(", ") || "Unknown.";
                const releaseDate = gameDetails.release_date?.date || "Not available.";
                const genres = gameDetails.genres?.map(genre => genre.description).join(", ") || "No genres.";

                const priceOverview = gameDetails.price_overview || {};
                const originalPrice = priceOverview.initial ? `Rp${(priceOverview.initial / 100).toLocaleString("id-ID")}` : "Free";
                const finalPrice = priceOverview.final ? `Rp${(priceOverview.final / 100).toLocaleString("id-ID")}` : originalPrice;
                const discount = priceOverview.discount_percent > 0 ? ` (-${priceOverview.discount_percent}%)` : "";
                const priceText = finalPrice !== originalPrice ? `~~${originalPrice}~~ ➜ **${finalPrice}${discount}**` : `**${originalPrice}**`;
                
                const minRequirements = gameDetails.pc_requirements?.minimum
                  ? gameDetails.pc_requirements.minimum
                      .replace(/<\/?[^>]+(>|$)/g, "")
                      .replace(/Minimum:/g, "Minimum:\n")
                      .replace(/\*:/g, ":")
                      .replace(/&quot;/g, '"')
                      .replace(/(OS|Processor|Memory|Graphics|DirectX|Network|Storage|Sound Card|Additional Notes)/g, "\n$1")
                      .replace(/\n\n+/g, "\n")
                      .trim()
                  : "No minimum specs available.";
                // Create embed
                const embed = new EmbedBuilder()
                    .setColor("#1b2838")
                    .setTitle(`🎮 ${gameName}`)
                    .setURL(`https://store.steampowered.com/app/${gameId}`)
                    .setDescription(description)
                    .setThumbnail(gameImage)
                    .addFields(
                        { name: "💰 Price", value: priceText, inline: true },
                        { name: "🛠️ Developer", value: developer, inline: true },
                        { name: "📢 Publisher", value: publisher, inline: true },
                        { name: "🎭 Genre", value: genres, inline: true },
                        { name: "📅 Release Date", value: releaseDate, inline: true },
                        { name: "🖥️ Minimum Requirements", value: `\`\`\`${minRequirements}\`\`\`` }
                    )
                    .setFooter({ text: `Game ${index + 1} of ${games.length}` });

                return embed;
            };

            const createButtonRow = (index) => new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                    .setCustomId("prev_game")
                    .setLabel("◀ Prev")
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(index === 0),
                new ButtonBuilder()
                    .setCustomId("next_game")
                    .setLabel("Next ▶")
                    .setStyle(ButtonStyle.Secondary)
                    .setDisabled(index === games.length - 1),
                new ButtonBuilder()
                    .setLabel("View on Steam")
                    .setStyle(ButtonStyle.Link)
                    .setURL(`https://store.steampowered.com/app/${games[index].id}`)
            );

            let embed = await fetchGameDetails(currentIndex);
            if (!embed) return message.reply("❌ Could not fetch game details.");

            const sentMessage = await message.channel.send({
                embeds: [embed],
                components: [createButtonRow(currentIndex)]
            });

            const collector = sentMessage.createMessageComponentCollector({
                filter: i => i.user.id === message.author.id,
                time: 60000
            });

            collector.on("collect", async (interaction) => {
                await interaction.deferUpdate();

                if (interaction.customId === "prev_game") {
                    currentIndex--;
                } else if (interaction.customId === "next_game") {
                    currentIndex++;
                }

                let newEmbed = await fetchGameDetails(currentIndex);
                if (newEmbed) {
                    await sentMessage.edit({
                        embeds: [newEmbed],
                        components: [createButtonRow(currentIndex)]
                    });
                }
            });

            collector.on("end", () => {
                sentMessage.edit({ components: [] }).catch(() => {});
            });

        } catch (error) {
            console.error("Error fetching Steam data:", error);
            return message.reply("❌ An error occurred while fetching game data. Please try again later.");
        }
    }
};
