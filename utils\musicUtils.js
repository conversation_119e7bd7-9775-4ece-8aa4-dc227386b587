const fs = require('fs');

/**
 * Safely delete audio files with retry mechanism for Windows EBUSY errors
 * @param {string} filePath - Path to the file to delete
 * @param {number} retries - Number of retry attempts (default: 3)
 * @param {number} delay - Delay between retries in milliseconds (default: 1000)
 */
function safeDeleteFile(filePath, retries = 3, delay = 1000) {
    if (!filePath || !fs.existsSync(filePath)) return;

    const attemptDelete = (attempt) => {
        fs.unlink(filePath, (err) => {
            if (err) {
                if (err.code === 'EBUSY' && attempt < retries) {
                    // File is busy, retry after delay
                    console.log(`File ${filePath} is busy, retrying in ${delay}ms... (attempt ${attempt + 1}/${retries})`);
                    setTimeout(() => attemptDelete(attempt + 1), delay);
                } else if (err.code === 'ENOENT') {
                    // File doesn't exist anymore, that's fine
                    return;
                } else {
                    console.error(`<PERSON>rror deleting file ${filePath} after ${attempt} attempts:`, err.message);
                }
            }
            // Success - file deleted
        });
    };

    attemptDelete(1);
}

/**
 * Delete file with a delay to ensure audio resource is released
 * @param {string} filePath - Path to the file to delete
 * @param {number} delayMs - Delay before attempting deletion (default: 500ms)
 */
function delayedDeleteFile(filePath, delayMs = 500) {
    if (!filePath) return;

    setTimeout(() => {
        safeDeleteFile(filePath);
    }, delayMs);
}

/**
 * Clean up multiple files with delay
 * @param {Array<string>} filePaths - Array of file paths to delete
 * @param {number} delayMs - Delay before attempting deletion (default: 500ms)
 */
function cleanupFiles(filePaths, delayMs = 500) {
    if (!Array.isArray(filePaths)) return;

    setTimeout(() => {
        filePaths.forEach(filePath => {
            if (filePath) {
                safeDeleteFile(filePath);
            }
        });
    }, delayMs);
}

/**
 * Create a stable audio resource with optimized settings
 * @param {string} filePath - Path to the audio file
 * @param {object} options - Additional options
 */
function createStableAudioResource(filePath, options = {}) {
    const { createAudioResource } = require('@discordjs/voice');

    return createAudioResource(filePath, {
        inlineVolume: true,
        inputType: 'arbitrary',
        metadata: options.metadata || {},
        // Add silence padding to prevent audio glitches
        silencePaddingFrames: 5,
        ...options
    });
}

/**
 * Monitor audio player for stability issues with proper cleanup
 * @param {AudioPlayer} player - The audio player to monitor
 * @param {string} guildId - Guild ID for logging
 * @returns {Function} Cleanup function to remove listeners
 */
function monitorPlayerStability(player, guildId) {
    let lastStateChange = Date.now();
    let rapidStateChanges = 0;

    const stateChangeHandler = (oldState, newState) => {
        const now = Date.now();
        const timeSinceLastChange = now - lastStateChange;

        // Detect rapid state changes (potential audio issues)
        if (timeSinceLastChange < 1000) {
            rapidStateChanges++;
            if (rapidStateChanges > 3) {
                console.warn(`Rapid audio state changes detected for guild ${guildId}. This may indicate network or audio issues.`);
                rapidStateChanges = 0; // Reset counter
            }
        } else {
            rapidStateChanges = 0; // Reset if enough time has passed
        }

        lastStateChange = now;
        console.log(`Audio player state changed from ${oldState.status} to ${newState.status} for guild ${guildId}`);
    };

    const errorHandler = (error) => {
        console.error(`Audio player error for guild ${guildId}:`, error);
    };

    player.on('stateChange', stateChangeHandler);
    player.on('error', errorHandler);

    // Return cleanup function
    return () => {
        player.removeListener('stateChange', stateChangeHandler);
        player.removeListener('error', errorHandler);
    };
}

/**
 * Clean up audio resources and processes
 * @param {object} audioResource - Audio resource to clean up
 * @param {object} ffmpegProcess - FFmpeg process to clean up
 */
function cleanupAudioResources(audioResource, ffmpegProcess) {
    try {
        // Clean up audio resource
        if (audioResource && typeof audioResource.destroy === 'function') {
            audioResource.destroy();
        }

        // Clean up FFmpeg process
        if (ffmpegProcess && typeof ffmpegProcess.kill === 'function') {
            ffmpegProcess.kill('SIGTERM');

            // Remove from global tracking if available
            if (global.client && global.client.activeProcesses) {
                global.client.activeProcesses.delete(ffmpegProcess);
            }
        }
    } catch (error) {
        console.error('Error cleaning up audio resources:', error);
    }
}

/**
 * Memory optimization - force garbage collection if available
 */
function optimizeMemory() {
    try {
        if (global.gc) {
            global.gc();
            console.log('🧹 Garbage collection completed');
        }
    } catch (error) {
        console.warn('⚠️ Could not run garbage collection:', error.message);
    }
}

module.exports = {
    safeDeleteFile,
    delayedDeleteFile,
    cleanupFiles,
    createStableAudioResource,
    monitorPlayerStability,
    cleanupAudioResources,
    optimizeMemory
};
