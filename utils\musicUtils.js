const fs = require('fs');

/**
 * Safely delete audio files with retry mechanism for Windows EBUSY errors
 * @param {string} filePath - Path to the file to delete
 * @param {number} retries - Number of retry attempts (default: 3)
 * @param {number} delay - Delay between retries in milliseconds (default: 1000)
 */
function safeDeleteFile(filePath, retries = 3, delay = 1000) {
    if (!filePath || !fs.existsSync(filePath)) return;

    const attemptDelete = (attempt) => {
        fs.unlink(filePath, (err) => {
            if (err) {
                if (err.code === 'EBUSY' && attempt < retries) {
                    // File is busy, retry after delay
                    console.log(`File ${filePath} is busy, retrying in ${delay}ms... (attempt ${attempt + 1}/${retries})`);
                    setTimeout(() => attemptDelete(attempt + 1), delay);
                } else if (err.code === 'ENOENT') {
                    // File doesn't exist anymore, that's fine
                    return;
                } else {
                    console.error(`<PERSON>rror deleting file ${filePath} after ${attempt} attempts:`, err.message);
                }
            }
            // Success - file deleted
        });
    };

    attemptDelete(1);
}

/**
 * Delete file with a delay to ensure audio resource is released
 * @param {string} filePath - Path to the file to delete
 * @param {number} delayMs - Delay before attempting deletion (default: 500ms)
 */
function delayedDeleteFile(filePath, delayMs = 500) {
    if (!filePath) return;

    setTimeout(() => {
        safeDeleteFile(filePath);
    }, delayMs);
}

/**
 * Clean up multiple files with delay
 * @param {Array<string>} filePaths - Array of file paths to delete
 * @param {number} delayMs - Delay before attempting deletion (default: 500ms)
 */
function cleanupFiles(filePaths, delayMs = 500) {
    if (!Array.isArray(filePaths)) return;

    setTimeout(() => {
        filePaths.forEach(filePath => {
            if (filePath) {
                safeDeleteFile(filePath);
            }
        });
    }, delayMs);
}

/**
 * Create a stable audio resource with optimized settings
 * @param {string} filePath - Path to the audio file
 * @param {object} options - Additional options
 */
function createStableAudioResource(filePath, options = {}) {
    const { createAudioResource } = require('@discordjs/voice');

    return createAudioResource(filePath, {
        inlineVolume: true,
        inputType: 'arbitrary',
        metadata: options.metadata || {},
        // Add silence padding to prevent audio glitches
        silencePaddingFrames: 5,
        ...options
    });
}

/**
 * Monitor audio player for stability issues
 * @param {AudioPlayer} player - The audio player to monitor
 * @param {string} guildId - Guild ID for logging
 */
function monitorPlayerStability(player, guildId) {
    let lastStateChange = Date.now();
    let rapidStateChanges = 0;

    player.on('stateChange', (oldState, newState) => {
        const now = Date.now();
        const timeSinceLastChange = now - lastStateChange;

        // Detect rapid state changes (potential audio issues)
        if (timeSinceLastChange < 1000) {
            rapidStateChanges++;
            if (rapidStateChanges > 3) {
                console.warn(`Rapid audio state changes detected for guild ${guildId}. This may indicate network or audio issues.`);
                rapidStateChanges = 0; // Reset counter
            }
        } else {
            rapidStateChanges = 0; // Reset if enough time has passed
        }

        lastStateChange = now;
        console.log(`Audio player state changed from ${oldState.status} to ${newState.status} for guild ${guildId}`);
    });

    player.on('error', (error) => {
        console.error(`Audio player error for guild ${guildId}:`, error);
    });
}

module.exports = {
    safeDeleteFile,
    delayedDeleteFile,
    cleanupFiles,
    createStableAudioResource,
    monitorPlayerStability
};
