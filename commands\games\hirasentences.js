const { <PERSON>Row<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, EmbedBuilder, MessageFlags } = require('discord.js');
const fs = require('fs');
const path = require('path');

// Load dataset
const dataset = JSON.parse(fs.readFileSync(path.join(__dirname, '../../utils/hiragana_dataset.json'), 'utf8'));

function shuffleArray(array) {
  for (let i = array.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [array[i], array[j]] = [array[j], array[i]];
  }
  return array;
}

function getRandomQuizSet(count = 4) {
  return shuffleArray([...dataset]).slice(0, count);
}

module.exports = {
  name: 'hirasen',
  aliases: ['hisen'],
  category: 'game',
  description: 'Practice Hiragana sentence reading with a translation quiz!',
  usePrefix: true,
  isEverywhere: false,

  async execute(message, args) {
    const loadingMsg = await message.channel.send('Preparing Hiragana quiz...');

    let score = 0;
    let health = Math.min(parseInt(args[0]) || 3, 7);
    const timeout = Math.min(parseInt(args[1]) || 30, 280) * 1000;
    let stopped = false;

    while (health > 0 && !stopped) {
      const sentenceList = getRandomQuizSet(4);
      const current = sentenceList[0];
      const correct = current.translation;

      const distractors = sentenceList.slice(1).map(item => item.translation);
      const allOptions = shuffleArray([correct, ...distractors]);

      const letters = ['A', 'B', 'C', 'D'];
      const correctIndex = allOptions.indexOf(correct);
      const correctLetter = letters[correctIndex];

      const quizEmbed = new EmbedBuilder()
        .setColor(0x00bcd4)
        .setTitle('🌀 Hiragana Sentence Quiz')
        .setDescription(`What does this sentence mean?\n\n# ${current.sentence}`)
        .addFields({
          name: 'Choices',
          value: allOptions.map((opt, i) => `**${letters[i]})** ${opt}`).join('\n')
        })
        .setFooter({
          text: `Player: ${message.author.username} | Health: ${'💖'.repeat(health)} | Score: ${score} | Time: ${timeout / 1000}s`
        });

      const row = new ActionRowBuilder().addComponents(
        ...letters.map(letter =>
          new ButtonBuilder()
            .setCustomId(letter)
            .setLabel(letter)
            .setStyle(ButtonStyle.Primary)
        ),
        new ButtonBuilder()
          .setCustomId('STOP')
          .setLabel('Stop')
          .setStyle(ButtonStyle.Danger)
      );

      const quizMsg = await loadingMsg.edit({ content: '', embeds: [quizEmbed], components: [row] });

      const collector = quizMsg.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: timeout
      });

      let answered = false;

      const countdownEmojis = ['3️⃣', '2️⃣', '1️⃣'];
      const timeoutTimers = countdownEmojis.map((emoji, i) =>
        setTimeout(async () => {
          if (!answered) {
            try {
              await quizMsg.react(emoji);
            } catch (e) {}
          }
        }, timeout - (3 - i) * 1000)
      );

      async function clearReactions() {
        try {
          const msg = await quizMsg.fetch();
          for (const emoji of countdownEmojis) {
            const reaction = msg.reactions.resolve(emoji);
            if (reaction) await reaction.remove();
          }
        } catch (e) {}
      }

      collector.on('collect', async interaction => {
        timeoutTimers.forEach(clearTimeout);
        answered = true;
        await clearReactions();
        collector.stop();

        if (interaction.customId === 'STOP') {
          stopped = true;
          await interaction.update({
            content: `🛑 Game stopped by ${message.author.username}. Final score: **${score}**`,
            embeds: [],
            components: []
          });
          return;
        }

        if (interaction.customId === correctLetter) {
          score++;
          await interaction.update({
            content: `✅ Correct! ${correctLetter}) ${correct}`,
            embeds: [],
            components: []
          });
        } else {
          health--;
          if (health === 0) {
            await interaction.update({
              content: `💀 No more health! The correct answer was **${correctLetter}) ${correct}**.`,
              embeds: [],
              components: []
            });
          } else {
            await interaction.reply({
              content: `❌ Wrong! Health left: ${'💖'.repeat(health)}. The correct answer was: **${correctLetter}) ${correct}**`,
              flags: MessageFlags.Ephemeral
            });
          }
        }
      });

      await new Promise(resolve => {
        collector.on('end', async () => {
          timeoutTimers.forEach(clearTimeout);
          if (!answered && !stopped) {
            health--;
            await quizMsg.edit({
              content: `⏰ Time's up! The correct answer was **${correctLetter}) ${correct}**. Health left: ${'💖'.repeat(health)}`,
              embeds: [],
              components: []
            });
          }
          await clearReactions();
          resolve();
        });
      });
    }

    if (!stopped) {
      await message.channel.send(`🎉 Quiz over! Your score: **${score}**. Thanks for playing!`);
    }
  }
};
