const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle } = require('discord.js');
const { Chess } = require('chess.js');
const axios = require('axios');

module.exports = {
    name: 'chess',
    aliases: ['catur'],
    category: 'game',
    description: 'Starts a chess game. Mention a user to play against someone else, or play both sides yourself if no user is mentioned.',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const chess = new Chess();
        let gameActive = true;
        let boardMessage;
        let lastMove = "";
        let invalidMoveMessage = "";
        let buttonCollector, moveCollector;
        let lastMoveTime = 0;

        const botMentioned = message.mentions.has(message.client.user);
        const playerWhite = message.author;
        const playerBlack = botMentioned ? message.client.user : (message.mentions.users.first() || message.author);

        function formatFenForImage(fen) {
            return fen.split(' ')[0].replace(/\//g, '');
        }

        function getCurrentPlayerAvatar() {
            return chess.turn() === 'w' ? playerWhite.displayAvatarURL() : playerBlack.displayAvatarURL();
        }

        async function sendChessboard(isRefresh = false) {
            try {
                if (isRefresh && boardMessage) {
                    await boardMessage.delete().catch(() => {});
                    boardMessage = null;
                }

                const lastMoves = chess.history().slice(-5).map(m => `• ${m}`).join("\n") || "None";
                let image_url = lastMove ? `https://chessboardimage.com/${formatFenForImage(chess.fen())}-${lastMove}.png` : `https://chessboardimage.com/${formatFenForImage(chess.fen())}.png`;
                const embed = new EmbedBuilder()
                    .setTitle('♟️ Chess Game')
                    .setDescription(`Turn: ${chess.turn() === 'w' ? 'White' : 'Black'}\nWhite: ${playerWhite}\nBlack: ${playerBlack}`)
                    .setThumbnail(getCurrentPlayerAvatar())
                    .setImage(image_url)
                    .setColor(0x0099ff)
                    .addFields({ name: "Last Moves", value: lastMoves });

                if (invalidMoveMessage) {
                    embed.setFooter({ text: invalidMoveMessage });
                } else {
                    embed.setFooter({ text: "Make a move by typing in algebraic notation (e.g., e2e4, O-O, e7e8q)" });
                }

                const buttons = new ActionRowBuilder().addComponents(
                    new ButtonBuilder().setCustomId('reinvoke_chess').setLabel('♻️ Refresh').setStyle(ButtonStyle.Primary),
                    new ButtonBuilder().setCustomId('resign_chess').setLabel('🏳️ Resign').setStyle(ButtonStyle.Secondary),
                    new ButtonBuilder().setCustomId('stop_chess').setLabel('🛑 Stop').setStyle(ButtonStyle.Danger)
                );

                if (boardMessage) {
                    try {
                        await boardMessage.edit({ embeds: [embed], components: [buttons] });
                    } catch (error) {
                        boardMessage = await message.channel.send({ embeds: [embed], components: [buttons] });
                        createButtonCollector();
                    }
                } else {
                    boardMessage = await message.channel.send({ embeds: [embed], components: [buttons] });
                    createButtonCollector();
                }
            } catch (error) {
                console.error("Error updating chessboard:", error.message);
            }
        }

        function createButtonCollector() {
            if (!boardMessage) return;

            if (buttonCollector) {
                buttonCollector.stop();
            }

            buttonCollector = boardMessage.createMessageComponentCollector({ time: 300000 });

            buttonCollector.on('collect', async interaction => {
                if (!gameActive) return;

                try {
                    if (interaction.customId === 'stop_chess') {
                        gameActive = false;
                        buttonCollector.stop();
                        moveCollector?.stop();
                        await boardMessage.edit({ content: '🛑 **Chess game has ended.**', components: [] }).catch(() => {});
                    } else if (interaction.customId === 'resign_chess') {
                        gameActive = false;
                        await boardMessage.edit({ content: `🏳️ **${interaction.user} resigned!**`, components: [] }).catch(() => {});
                    } else if (interaction.customId === 'reinvoke_chess') {
                        await sendChessboard(true);
                    }
                    await interaction.deferUpdate();
                } catch (error) {
                    console.error("Error processing button interaction:", error.message);
                }
            });

            buttonCollector.on('end', async () => {
                if (boardMessage && gameActive) {
                    await boardMessage.edit({ components: [] }).catch(() => {});
                }
            });
        }

        async function getStockfishMove(fen) {
            try {
                const response = await axios.post("https://chess-api.com/v1", {
                    fen: fen,
                    depth: 12,
                    maxThinkingTime: 50
                });
                if (response.data && response.data.move) {
                    return response.data.move;
                }
            } catch (error) {
                console.error("Chess API error:", error.message);
            }
            return null;
        }

        await sendChessboard();

        const moveFilter = m => {
            if (!gameActive) return false;
            if (!botMentioned && playerWhite.id !== playerBlack.id) {
                if (chess.turn() === 'w' && m.author.id !== playerWhite.id) return false;
                if (chess.turn() === 'b' && m.author.id !== playerBlack.id) return false;
            }
            return /^[a-h][1-8][a-h][1-8][qrbn]?$/i.test(m.content) || /^O-O(-O)?$/i.test(m.content);
        };

        moveCollector = message.channel.createMessageCollector({ filter: moveFilter, time: 300000 });

        moveCollector.on('collect', async m => {
            if (!gameActive) return;

            const now = Date.now();
            if (now - lastMoveTime < 1000) return;
            lastMoveTime = now;

            try {
                await m.delete().catch(() => {});

                let move = {
                    from: m.content.substring(0, 2).toLowerCase(),
                    to: m.content.substring(2, 4).toLowerCase(),
                    promotion: m.content[4] ? m.content[4].toLowerCase() : undefined
                };

                let result;
                try {
                    result = chess.move(move);
                } catch (error) {
                    result = null;
                }

                if (!result) {
                    invalidMoveMessage = `⚠️ Invalid move: ${m.content}. Try again.`;
                    await sendChessboard();
                    return;
                }

                invalidMoveMessage = "";
                lastMove = `${move.from}${move.to}`;

                if (chess.isGameOver()) {
                    gameActive = false;
                    moveCollector.stop();
                    buttonCollector?.stop();
                    let endMessage = chess.isCheckmate()
                        ? `🏆 Checkmate! **${chess.turn() === 'w' ? playerBlack : playerWhite}** wins!`
                        : '⚖️ Draw!';
                    await sendChessboard();
                    setTimeout(async () => {
                        if (boardMessage) {
                            await boardMessage.edit({ content: endMessage, components: [] }).catch(() => {});
                        }
                    }, 1000);
                    return;
                }

                await sendChessboard();

               
                if (botMentioned && chess.turn() === 'b') {
                    const aiMove = await getStockfishMove(chess.fen());
                    if (aiMove) {
                        try {
                            chess.move(aiMove);
                            lastMove = aiMove;
                            await sendChessboard();

                            // Check if AI's move ended the game
                            if (chess.isGameOver()) {
                                gameActive = false;
                                moveCollector?.stop();
                                buttonCollector?.stop();
                                let endMessage = chess.isCheckmate()
                                    ? `🏆 Checkmate! **${chess.turn() === 'w' ? playerBlack : playerWhite}** wins!`
                                    : '⚖️ Draw!';
                                setTimeout(async () => {
                                    if (boardMessage) {
                                        await boardMessage.edit({ content: endMessage, components: [] }).catch(() => {});
                                    }
                                }, 1000);
                            }
                        } catch (error) {
                            console.error("AI made invalid move:", error.message);
                        }
                    }
                }
            } catch (error) {
                console.error("Error processing move:", error.message);
            }
        });
    }
};