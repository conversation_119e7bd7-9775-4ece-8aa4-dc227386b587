const {
    ContextMenuCommandBuilder,
    ApplicationCommandType,
    EmbedBuilder,
    ActionRowBuilder,
    ButtonBuilder,
    ButtonStyle,
    MessageFlags
} = require('discord.js');
const axios = require('axios');

module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('Reverse Search Image')
        .setType(ApplicationCommandType.Message),

    async execute(client, interaction) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });

        const message = interaction.targetMessage;
        let imageUrl;
        
        // Extract image URL from attachment
        const attachment = message.attachments.first();
        if (attachment && attachment.contentType?.startsWith('image/')) {
            imageUrl = attachment.url;
        }
        
        // Extract image URL from message content if not found
        if (!imageUrl) {
            const urlRegex = /(https?:\/\/.*\.(?:png|jpg|jpeg|gif|webp))/i;
            const urlMatch = message.content.match(urlRegex);
            if (urlMatch) imageUrl = urlMatch[1];
        }
        
        // Extract image URL from embed (main image or thumbnail) if not found
        if (!imageUrl && message.embeds.length > 0) {
            const embed = message.embeds[0];
            imageUrl = embed.image?.url || embed.thumbnail?.url;
        }
        
        if (!imageUrl) {
            return interaction.editReply({
                content: 'No valid image found. Please upload an image, provide an image URL, or ensure the embed has an image.',
            });
        }

        try {
            const encodedUrl = encodeURIComponent(imageUrl);
            const res = await axios.get(`https://saucenao.com/search.php?db=999&output_type=2&numres=5&api_key=${process.env.sauce}&url=${encodedUrl}`, {
                timeout: 10000 // Timeout 10 detik
            });

            const results = res.data.results;
            if (!results?.length) {
                return interaction.editReply({ content: 'No similar images found.' });
            }

            let currentIndex = 0;
            const totalResults = results.length;

            const getColor = (similarity) => {
                const sim = parseFloat(similarity);
                return sim > 85 ? '#2ecc71' : sim > 70 ? '#f1c40f' : '#e74c3c';
            };

            const capitalizeKey = (key) => key
                .replace(/_/g, ' ')
                .replace(/\b\w/g, char => char.toUpperCase());

            const getDetectedSources = (data) => Object.entries(data)
                .filter(([_, value]) => value && typeof value !== 'object')
                .map(([key, value]) => `**${capitalizeKey(key)}:** ${value}`)
                .join('\n') || 'Unknown';

                const getEmbed = (index) => {
                    const sauce = results[index];
                    const extUrls = sauce.data.ext_urls || [];
                    const similarity = sauce.header.similarity;
                    const detectedSources = getDetectedSources(sauce.data);
                
                    // Improved title handling
                    const title = sauce.data.title 
                        ? sauce.data.title.slice(0, 256) 
                        : 'Untitled';
                
                    // Additional metadata fields
                    const metadata = [];
                    if (sauce.data.member_name) metadata.push(`**Creator:** ${sauce.data.member_name}`);
                    if (sauce.data.creator) metadata.push(`**Artist:** ${sauce.data.creator}`);
                    if (sauce.data.characters) metadata.push(`**Characters:** ${sauce.data.characters}`);
                
                    // Improved field organization
                    const fields = [
                        { name: '🔍 Similarity', value: `${similarity}%`, inline: true },
                        { name: '🌐 Source', value: sauce.data.source || 'Unknown', inline: true },
                    ];
                
                    if (metadata.length > 0) {
                        fields.push({ 
                            name: '📖 Additional Info', 
                            value: metadata.join('\n').slice(0, 1024),
                            inline: false 
                        });
                    }
                
                    if (detectedSources) {
                        fields.push({
                            name: '🔎 Detected Details',
                            value: detectedSources.slice(0, 1024),
                            inline: false
                        });
                    }
                
                    if (extUrls.length > 0) {
                        fields.push({
                            name: '📌 External Links',
                            value: extUrls.map((url, i) => `[Link ${i + 1}](${url})`).join('\n').slice(0, 1024),
                            inline: false
                        });
                    }
                
                    return new EmbedBuilder()
                        .setColor(getColor(similarity))
                        .setTitle(title)
                        .setURL(extUrls[0] || null)
                        .setDescription(`**Reverse Search Results** [Original Image](${imageUrl})`)
                        .setThumbnail(imageUrl) // Show searched image as thumbnail
                        .setImage(sauce.header.thumbnail) // Show result preview as main image
                        .addFields(fields)
                        .setFooter({ 
                            text: `Result ${index + 1}/${totalResults} • Powered by SauceNAO`, 
                            iconURL: interaction.user.displayAvatarURL() 
                        })
                        .setTimestamp();
                };

            const getActionRow = (index) => new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                    .setCustomId('prev')
                    .setLabel('⬅️ Previous')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(index === 0),
                new ButtonBuilder()
                    .setCustomId('next')
                    .setLabel('Next ➡️')
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(index === totalResults - 1),
            );

            // Send initial embed
            const messageReply = await interaction.editReply({
                embeds: [getEmbed(currentIndex)],
                components: [getActionRow(currentIndex)],
                fetchReply: true,
            });

            const filter = (buttonInteraction) => buttonInteraction.user.id === interaction.user.id;
            const collector = messageReply.createMessageComponentCollector({ filter, time: 600000 });

            // Update the collector handlers with error protection
            collector.on('collect', async (buttonInteraction) => {
                try {
                    if (buttonInteraction.customId === 'prev') currentIndex--;
                    if (buttonInteraction.customId === 'next') currentIndex++;

                    await buttonInteraction.deferUpdate();
                    
                    // Check if message still exists
                    const channel = await client.channels.fetch(interaction.channelId);
                    const message = await channel.messages.fetch(messageReply.id).catch(() => null);
                    
                    if (!message) {
                        collector.stop();
                        return;
                    }

                    await interaction.editReply({
                        embeds: [getEmbed(currentIndex)],
                        components: [getActionRow(currentIndex)],
                    });
                } catch (error) {
                    console.error('Button interaction error:', error);
                    collector.stop();
                }
            });
            
            // Update the end handler with existence check
            collector.on('end', async () => {
                try {
                    const channel = await client.channels.fetch(interaction.channelId);
                    const message = await channel.messages.fetch(messageReply.id).catch(() => null);
                    
                    if (!message) return;

                    const disabledRow = new ActionRowBuilder().addComponents(
                        new ButtonBuilder()
                            .setCustomId('prev')
                            .setLabel('⬅️ Previous')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(true),
                        new ButtonBuilder()
                            .setCustomId('next')
                            .setLabel('Next ➡️')
                            .setStyle(ButtonStyle.Secondary)
                            .setDisabled(true)
                    );

                    await message.edit({ components: [disabledRow] });
                } catch (error) {
                    if (error.code !== 10008) { // Ignore "Unknown Message" errors
                        console.error('Final edit error:', error);
                    }
                }
            });
            
        } catch (error) {
            console.error('Error fetching SauceNAO results:', error);
            await interaction.editReply({
                content: 'An error occurred while retrieving the search results. Please try again later.',
            });
        }
    },
};
