const axios = require("axios");
const { EmbedBuilder } = require("discord.js");
const { format, parseISO } = require("date-fns");
const { id } = require("date-fns/locale");

module.exports = {
    name: "cuaca",
    aliases: ["weather"],
    category: "utility",
    description: "Menampilkan informasi cuaca terkini",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const location = args.join(" ");
        if (!location) {
            return message.reply("❌ Harap berikan nama lokasi!");
        }

        const getWeatherData = async (url) => {
            try {
                const response = await axios.get(url);
                return response.data;
            } catch (error) {
                if (error.response && error.response.status === 503) {
                    return null;
                } else {
                    throw error;
                }
            }
        };

        try {
            let data = await getWeatherData(
                `https://wttr.in/${encodeURI(location)}?format=%C|%t|%w|%h|%p|%Z|%f|%m|%u`
            );

            let timezone = "";
            if (!data) {
                data = await getWeatherData(
                    `https://wttr.in/${encodeURI(location)}?format=%C|%t|%w|%h|%p|%f|%m|%u`
                );
            } else {
                timezone = data.split("|")[5]?.trim();
            }

            const weatherData = data.split("|");
            const [
                condition,      // Cuaca (Cerah, Berawan, dll.)
                temperature,    // Suhu sekarang
                wind,           // Kecepatan angin
                humidity,       // Kelembaban
                precipitation,  // Curah hujan
                _timezone,      // Zona waktu
                feelsLike,      // Suhu terasa seperti
                visibility,     // Jarak pandang
                uvIndex         // Indeks UV
            ] = weatherData;

            // Ambil waktu lokal jika tersedia
            const localTime = timezone
                ? new Date().toLocaleTimeString("id-ID", {
                      hour: "2-digit",
                      minute: "2-digit",
                      timeZone: timezone,
                  })
                : null;

            // Ambil data JSON cuaca untuk detail tambahan
            const jsonWeather = await axios.get(
                `https://wttr.in/${encodeURI(location)}?format=j1`
            );

            const rawDate = jsonWeather.data.weather[0].date;
            const formattedDate = format(parseISO(rawDate), "EEEE, dd MMMM yyyy", {
                locale: id,
            });

            const sunrise = jsonWeather.data.weather[0].astronomy[0].sunrise;
            const sunset = jsonWeather.data.weather[0].astronomy[0].sunset;

            // Generate URL gambar thumbnail dari wttr.in
            const thumbnailUrl = `https://wttr.in/${encodeURI(location)}_0pq.png`;

            // Buat embed Discord
            const embed = new EmbedBuilder()
                .setColor("#1E88E5")
                .setTitle(`🌏 Cuaca di ${location.charAt(0).toUpperCase() + location.slice(1).toLowerCase()}`)
                .setThumbnail(thumbnailUrl) // Tambahkan thumbnail gambar cuaca
                .addFields(
                    { name: "📅 Tanggal", value: formattedDate, inline: false },
                    localTime ? { name: "🕒 Waktu Lokal", value: localTime, inline: false } : null,
                    { name: "🌤 Kondisi", value: condition || "N/A", inline: true },
                    { name: "🌡 Suhu", value: temperature || "N/A", inline: true },
                    feelsLike ? { name: "🔥 Terasa Seperti", value: feelsLike, inline: true } : null,
                    { name: "💨 Angin", value: wind || "N/A", inline: true },
                    { name: "💧 Kelembaban", value: humidity || "N/A", inline: true },
                    { name: "🌧️ Curah Hujan", value: precipitation || "N/A", inline: true },
                    visibility ? { name: "👀 Jarak Pandang", value: visibility, inline: true } : null,
                    uvIndex ? { name: "🌞 Indeks UV", value: uvIndex, inline: true } : null,
                    sunrise ? { name: "🌅 Matahari Terbit", value: sunrise, inline: true } : null,
                    sunset ? { name: "🌇 Matahari Terbenam", value: sunset, inline: true } : null
                )
                .setTimestamp();

            await message.reply({ embeds: [embed] });
        } catch (error) {
            console.error("Error fetching weather data:", error);
            message.reply("Error saat mencari data cuaca. Silakan coba lagi nanti.");
        }
    },
};
