const { EmbedBuilder } = require('discord.js');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

function parsePosition(input, queueLength) {
    input = input.toLowerCase().trim();
    
    if (input === 'last') {
        return queueLength;
    } else if (input === 'first') {
        return 2; // Position 1 is currently playing, so first in queue is position 2
    } else {
        const position = parseInt(input);
        if (isNaN(position) || position < 1 || position > queueLength) {
            return null;
        }
        return position;
    }
}

module.exports = {
    name: 'move',
    description: 'Move a song to a different position in the queue',
    aliases: ['mv', 'reorder'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || serverQueue.songs.length <= 2) {
            const notEnoughSongsEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Not Enough Songs')
                .setDescription('Need at least 3 songs in the queue to move songs around!')
                .setTimestamp();
            return message.reply({ embeds: [notEnoughSongsEmbed] });
        }

        if (args.length < 2) {
            const helpEmbed = new EmbedBuilder()
                .setColor('#FFA500')
                .setTitle('🔄 Move Command Help')
                .setDescription('**Usage:** `move <from> <to>`\n\n**Examples:**\n• `move 5 2` - Move song from position 5 to position 2\n• `move last 2` - Move last song to position 2\n• `move 3 first` - Move song from position 3 to first in queue\n• `move 4 last` - Move song from position 4 to last')
                .addFields({
                    name: 'Current Queue',
                    value: `${serverQueue.songs.length} song${serverQueue.songs.length !== 1 ? 's' : ''} in queue`,
                    inline: true
                })
                .setFooter({ text: 'Note: Position 1 is the currently playing song and cannot be moved' })
                .setTimestamp();
            return message.reply({ embeds: [helpEmbed] });
        }

        const fromInput = args[0];
        const toInput = args[1];

        const fromPosition = parsePosition(fromInput, serverQueue.songs.length);
        const toPosition = parsePosition(toInput, serverQueue.songs.length);

        if (!fromPosition || !toPosition) {
            const invalidEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Invalid Position')
                .setDescription(`Invalid position. Use numbers between 1 and ${serverQueue.songs.length}, or "first"/"last"`)
                .setTimestamp();
            return message.reply({ embeds: [invalidEmbed] });
        }

        // Check if trying to move currently playing song
        if (fromPosition === 1) {
            const currentlyPlayingEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Cannot Move')
                .setDescription('Cannot move the currently playing song!')
                .setTimestamp();
            return message.reply({ embeds: [currentlyPlayingEmbed] });
        }

        // Check if trying to move to currently playing position
        if (toPosition === 1) {
            const cannotMoveToCurrentEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Cannot Move')
                .setDescription('Cannot move a song to the currently playing position!')
                .setTimestamp();
            return message.reply({ embeds: [cannotMoveToCurrentEmbed] });
        }

        // Check if from and to positions are the same
        if (fromPosition === toPosition) {
            const samePositionEmbed = new EmbedBuilder()
                .setColor('#FFA500')
                .setTitle('⚠️ Same Position')
                .setDescription('The song is already at that position!')
                .setTimestamp();
            return message.reply({ embeds: [samePositionEmbed] });
        }

        // Get the song to move
        const songToMove = serverQueue.songs[fromPosition - 1]; // Convert to 0-based index

        if (!songToMove) {
            const noSongEmbed = new EmbedBuilder()
                .setColor('#FF0000')
                .setTitle('❌ Song Not Found')
                .setDescription('No song found at the specified position.')
                .setTimestamp();
            return message.reply({ embeds: [noSongEmbed] });
        }

        // Remove the song from its current position
        serverQueue.songs.splice(fromPosition - 1, 1);

        // Calculate the new insertion index (adjust for the removed song)
        let insertIndex = toPosition - 1;
        if (toPosition > fromPosition) {
            insertIndex--; // Adjust because we removed an element before this position
        }

        // Insert the song at the new position
        serverQueue.songs.splice(insertIndex, 0, songToMove);

        // Also update the originalQueue for loop functionality
        const originalIndex = serverQueue.originalQueue.findIndex(s => s.id === songToMove.id && s.url === songToMove.url);
        if (originalIndex > -1) {
            const originalSong = serverQueue.originalQueue.splice(originalIndex, 1)[0];
            // Find appropriate position in originalQueue (this is more complex, so we'll just add it back)
            serverQueue.originalQueue.push(originalSong);
        }

        // Create success embed
        const moveEmbed = new EmbedBuilder()
            .setColor('#00FF00')
            .setTitle('🔄 Song Moved')
            .setDescription(`Moved **[${songToMove.title}](${songToMove.displayUrl || songToMove.url})**`)
            .setThumbnail(songToMove.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=Audio')
            .addFields(
                { 
                    name: 'From Position', 
                    value: fromPosition.toString(), 
                    inline: true 
                },
                { 
                    name: 'To Position', 
                    value: toPosition.toString(), 
                    inline: true 
                },
                { 
                    name: 'Requested by', 
                    value: songToMove.requestedBy || 'Unknown', 
                    inline: true 
                }
            )
            .setFooter({ text: `Queue has ${serverQueue.songs.length} song${serverQueue.songs.length !== 1 ? 's' : ''}` })
            .setTimestamp();

        // Show next few songs in the new order
        if (serverQueue.songs.length > 1) {
            const nextSongs = serverQueue.songs.slice(1, 4).map((song, index) => 
                `${index + 2}. [${song.title}](${song.displayUrl || song.url})`
            ).join('\n');

            if (nextSongs) {
                moveEmbed.addFields({
                    name: 'Next Up',
                    value: nextSongs + (serverQueue.songs.length > 4 ? '\n...' : ''),
                    inline: false
                });
            }
        }

        message.reply({ embeds: [moveEmbed] });
    },
};
