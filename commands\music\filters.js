const {
    createThemedEmbed,
    createStatusIndicator,
    EMOJIS
} = require('../../utils/embedTheme');
const {
    createStreamingResourceWithFilters,
    STREAMING_FILTERS
} = require('../../utils/streamingUtils');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

// Available audio filters with display information
const FILTERS = {
    'bass': {
        name: 'Bass Boost',
        description: 'Enhance low frequencies',
        emoji: '🔊'
    },
    'treble': {
        name: 'Treble Boost',
        description: 'Enhance high frequencies',
        emoji: '🎵'
    },
    'nightcore': {
        name: 'Nightcore',
        description: 'Higher pitch and faster tempo',
        emoji: '⚡'
    },
    'vaporwave': {
        name: 'Vaporwave',
        description: 'Lower pitch and slower tempo',
        emoji: '🌊'
    },
    'echo': {
        name: 'Echo',
        description: 'Add echo effect',
        emoji: '🔄'
    },
    'reverb': {
        name: 'Reverb',
        description: 'Add reverb effect',
        emoji: '🏛️'
    },
    'distortion': {
        name: 'Distortion',
        description: 'Add distortion effect',
        emoji: '🎸'
    },
    'robot': {
        name: 'Robot Voice',
        description: 'Robotic voice effect',
        emoji: '🤖'
    },
    'chipmunk': {
        name: 'Chipmunk',
        description: 'High-pitched chipmunk voice',
        emoji: '🐿️'
    },
    'deep': {
        name: 'Deep Voice',
        description: 'Lower pitch voice',
        emoji: '🎭'
    }
};



module.exports = {
    name: 'filters',
    description: 'Apply audio filters to the current song',
    aliases: ['filter', 'fx', 'effects'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || !serverQueue.playing || !serverQueue.currentSong) {
            const noMusicEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Music Playing`)
                .setDescription(`${createStatusIndicator('error', 'There is no music currently playing!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to start playing music.`);
            return message.reply({ embeds: [noMusicEmbed] });
        }

        if (args.length === 0) {
            // Show available filters
            const filterList = Object.entries(FILTERS).map(([key, filter]) =>
                `${filter.emoji} **${key}** - ${filter.description}`
            ).join('\n');

            const helpEmbed = createThemedEmbed('info')
                .setTitle(`${EMOJIS.MUSIC_NOTE} Available Audio Filters`)
                .setDescription('**Usage:** `filters <filter_name>` or `filters off`\n\n**Available Filters:**\n' + filterList)
                .addFields({
                    name: `${EMOJIS.SPARKLES} Examples`,
                    value: '• `filters bass` - Apply bass boost\n• `filters nightcore` - Apply nightcore effect\n• `filters off` - Remove all filters',
                    inline: false
                }, {
                    name: `${EMOJIS.INFO} Features`,
                    value: `${EMOJIS.LIGHTNING} Real-time streaming filters\n${EMOJIS.FIRE} No file downloads required\n${EMOJIS.MUSIC_NOTE} Instant filter application`,
                    inline: false
                })
                .setFooter({ text: 'Note: Filters are applied instantly without interrupting playback' });
            return message.reply({ embeds: [helpEmbed] });
        }

        const filterName = args[0].toLowerCase();

        // Handle removing filters
        if (filterName === 'off' || filterName === 'none' || filterName === 'clear') {
            const currentSong = serverQueue.currentSong;

            if (!currentSong.activeFilters || currentSong.activeFilters.length === 0) {
                const noFiltersEmbed = createThemedEmbed('warning')
                    .setTitle(`${EMOJIS.WARNING} No Filters Active`)
                    .setDescription(`${createStatusIndicator('warning', 'There are no filters currently applied to remove.')}\n\n${EMOJIS.INFO} Use \`filters\` to see available filters.`);
                return message.reply({ embeds: [noFiltersEmbed] });
            }

            try {
                // Calculate current playback position to preserve it
                let currentPosition = 0;
                if (serverQueue.startTime) {
                    currentPosition = Math.floor((Date.now() - serverQueue.startTime) / 1000);
                }

                // Create new streaming resource without filters, preserving position
                const resource = createStreamingResourceWithFilters(currentSong.streamingUrl, [], {
                    volume: serverQueue.volume,
                    seek: currentPosition,
                    metadata: { title: currentSong.title }
                });

                if (serverQueue.volume !== undefined && resource.volume) {
                    resource.volume.setVolume(serverQueue.volume / 100);
                }

                serverQueue.player.play(resource);
                currentSong.activeFilters = [];

                // Update start time to account for the seek
                serverQueue.startTime = Date.now() - (currentPosition * 1000);

                const clearedEmbed = createThemedEmbed('success')
                    .setTitle(`${EMOJIS.SUCCESS} Filters Cleared`)
                    .setDescription(`${createStatusIndicator('success', `Removed all filters from **${currentSong.title}**`)}\n\n${EMOJIS.MUSIC_NOTE} Now streaming original audio!`)
                    .setThumbnail(currentSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=Audio');

                message.reply({ embeds: [clearedEmbed] });
            } catch (error) {
                console.error('Error clearing filters:', error);
                const errorEmbed = createThemedEmbed('error')
                    .setTitle(`${EMOJIS.ERROR} Error`)
                    .setDescription(`${createStatusIndicator('error', 'Failed to clear filters!')}\n\n**Error:** ${error.message || 'Unknown error'}`);
                message.reply({ embeds: [errorEmbed] });
            }
            return;
        }

        // Check if filter exists
        if (!FILTERS[filterName]) {
            const invalidFilterEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Invalid Filter`)
                .setDescription(`${createStatusIndicator('error', `Filter "${filterName}" not found!`)}\n\n${EMOJIS.INFO} Use \`filters\` to see available filters.`);
            return message.reply({ embeds: [invalidFilterEmbed] });
        }

        const filter = FILTERS[filterName];
        const currentSong = serverQueue.currentSong;

        // Check if this filter is already applied
        if (currentSong.activeFilters && currentSong.activeFilters.includes(filterName)) {
            const alreadyAppliedEmbed = createThemedEmbed('warning')
                .setTitle(`${EMOJIS.WARNING} Filter Already Applied`)
                .setDescription(`${createStatusIndicator('warning', `${filter.emoji} **${filter.name}** is already applied to this song!`)}\n\n${EMOJIS.INFO} Use \`filters off\` to remove all filters first.`);
            return message.reply({ embeds: [alreadyAppliedEmbed] });
        }

        const processingEmbed = createThemedEmbed('warning')
            .setTitle(`${EMOJIS.LOADING} Applying Filter...`)
            .setDescription(`${createStatusIndicator('loading', `Applying ${filter.emoji} **${filter.name}** to **${currentSong.title}**`)}\n\n${EMOJIS.INFO} This may take a moment...`)
            .setThumbnail(currentSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=Audio');

        const processingMessage = await message.reply({ embeds: [processingEmbed] });

        try {
            // Calculate current playback position to preserve it
            let currentPosition = 0;
            if (serverQueue.startTime) {
                currentPosition = Math.floor((Date.now() - serverQueue.startTime) / 1000);
            }

            // Get current active filters and add the new one
            const currentFilters = currentSong.activeFilters || [];
            const newFilters = [...currentFilters, filterName];

            // Create new streaming resource with filters, preserving position
            const resource = createStreamingResourceWithFilters(currentSong.streamingUrl, newFilters, {
                volume: serverQueue.volume,
                seek: currentPosition,
                metadata: { title: currentSong.title }
            });

            if (serverQueue.volume !== undefined && resource.volume) {
                resource.volume.setVolume(serverQueue.volume / 100);
            }

            serverQueue.player.play(resource);

            // Update current song info
            currentSong.activeFilters = newFilters;

            // Update start time to account for the seek
            serverQueue.startTime = Date.now() - (currentPosition * 1000);

            const successEmbed = createThemedEmbed('success')
                .setTitle(`${EMOJIS.SUCCESS} Filter Applied`)
                .setDescription(`${createStatusIndicator('success', `Applied ${filter.emoji} **${filter.name}** to **${currentSong.title}**`)}\n\n${EMOJIS.MUSIC_NOTE} Now streaming with filter effects!`)
                .setThumbnail(currentSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=Audio')
                .addFields({
                    name: `${EMOJIS.SPARKLES} Active Filters`,
                    value: currentSong.activeFilters.map(f => {
                        const filterInfo = FILTERS[f];
                        return filterInfo ? `${filterInfo.emoji} ${filterInfo.name}` : f;
                    }).join(', '),
                    inline: false
                })
                .setFooter({ text: 'Use "filters off" to remove all filters' });

            await processingMessage.edit({ embeds: [successEmbed] });

        } catch (error) {
            console.error('Filter command error:', error);
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Filter Failed`)
                .setDescription(`${createStatusIndicator('error', 'An error occurred while applying the filter!')}\n\n**Error:** ${error.message || 'Unknown error'}\n\n${EMOJIS.INFO} The filter might not be supported or there may be a network issue.`);
            await processingMessage.edit({ embeds: [errorEmbed] }).catch(console.error);
        }
    },
};
