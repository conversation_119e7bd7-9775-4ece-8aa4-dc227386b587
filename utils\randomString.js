/**
 * Generates a random alphanumeric string of a given length.
 *
 * @param {number} length - The desired length of the string.
 * @returns {string} - A random alphanumeric string.
 */
function randomString(length = 10) {
    const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    return Array.from({ length }, () => chars[Math.floor(Math.random() * chars.length)]).join('');
}

module.exports = { randomString };
