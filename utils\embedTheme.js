const { EmbedBuilder } = require('discord.js');

/**
 * Music Bot Visual Theme System
 * Provides consistent colors, emojis, and styling across all music commands
 */

// Color Palette
const COLORS = {
    // Primary Colors
    PRIMARY: '#FF6B6B',        // Coral Red - Main brand color
    SECONDARY: '#4ECDC4',      // Teal - Secondary actions
    ACCENT: '#45B7D1',         // Blue - Information
    
    // Status Colors
    SUCCESS: '#96CEB4',        // Mint Green - Success states
    WARNING: '#FFEAA7',        // Light Yellow - Warnings
    ERROR: '#FF7675',          // Light Red - Errors
    INFO: '#74B9FF',           // Light Blue - Information
    
    // Music States
    PLAYING: '#00B894',        // Green - Currently playing
    PAUSED: '#FDCB6E',         // Orange - Paused state
    STOPPED: '#E17055',        // Red-Orange - Stopped
    LOADING: '#A29BFE',        // Purple - Loading/Processing
    
    // Special Effects
    FILTER: '#6C5CE7',         // Purple - Audio filters
    LOOP: '#FD79A8',           // Pink - Loop modes
    VOLUME: '#00CEC9',         // Cyan - Volume control
    QUEUE: '#0984E3',          // Blue - Queue operations
};

// Emoji Sets
const EMOJIS = {
    // Playback Controls
    PLAY: '▶️',
    PAUSE: '⏸️',
    STOP: '⏹️',
    SKIP: '⏭️',
    PREVIOUS: '⏮️',
    SHUFFLE: '🔀',
    REPEAT: '🔁',
    REPEAT_ONE: '🔂',
    
    // Volume & Audio
    VOLUME_HIGH: '🔊',
    VOLUME_MED: '🔉',
    VOLUME_LOW: '🔈',
    VOLUME_MUTE: '🔇',
    AUDIO_WAVE: '🎵',
    MUSIC_NOTE: '🎶',
    HEADPHONES: '🎧',
    SPEAKER: '📢',
    
    // Queue & Navigation
    QUEUE: '📋',
    LIST: '📝',
    LINK: '🔗',
    PLAYLIST: '📜',
    ARROW_UP: '⬆️',
    ARROW_DOWN: '⬇️',
    ARROW_RIGHT: '➡️',
    NUMBERS: ['1️⃣', '2️⃣', '3️⃣', '4️⃣', '5️⃣', '6️⃣', '7️⃣', '8️⃣', '9️⃣', '🔟'],
    SEARCH: '🔍',
    
    // Status & Feedback
    SUCCESS: '✅',
    ERROR: '❌',
    WARNING: '⚠️',
    INFO: 'ℹ️',
    LOADING: '⏳',
    PROCESSING: '🔄',
    QUESTION: '❓',
    
    // Music Features
    FILTER: '🎛️',
    EQUALIZER: '🎚️',
    MICROPHONE: '🎤',
    GUITAR: '🎸',
    PIANO: '🎹',
    DRUM: '🥁',
    TRUMPET: '🎺',
    VIOLIN: '🎻',
    
    // Time & Duration
    CLOCK: '🕐',
    TIMER: '⏰',
    HOURGLASS: '⏳',
    STOPWATCH: '⏱️',
    
    // Special Effects
    SPARKLES: '✨',
    STAR: '⭐',
    FIRE: '🔥',
    LIGHTNING: '⚡',
    RAINBOW: '🌈',
    CRYSTAL: '💎',
    
    // User & Social
    USER: '👤',
    USERS: '👥',
    CROWN: '👑',
    HEART: '❤️',
    THUMBS_UP: '👍',
    CLAP: '👏',
};

// Progress Bar Characters
const PROGRESS_CHARS = {
    FILLED: '█',
    EMPTY: '░',
    CURRENT: '🔘',
    START: '▶',
    END: '◀',
};

/**
 * Create a progress bar visualization
 * @param {number} current - Current position (0-100)
 * @param {number} total - Total length (default 20 characters)
 * @returns {string} Progress bar string
 */
function createProgressBar(current, total = 20) {
    const percentage = Math.max(0, Math.min(100, current));
    const filled = Math.round((percentage / 100) * total);
    const empty = total - filled;
    
    const filledBar = PROGRESS_CHARS.FILLED.repeat(filled);
    const emptyBar = PROGRESS_CHARS.EMPTY.repeat(empty);
    
    return `${filledBar}${emptyBar}`;
}

/**
 * Create a time-based progress bar
 * @param {number} currentSeconds - Current time in seconds
 * @param {number} totalSeconds - Total duration in seconds
 * @returns {string} Time progress bar with timestamps
 */
function createTimeProgressBar(currentSeconds, totalSeconds) {
    const percentage = totalSeconds > 0 ? (currentSeconds / totalSeconds) * 100 : 0;
    const progressBar = createProgressBar(percentage, 15);
    
    const currentTime = formatTime(currentSeconds);
    const totalTime = formatTime(totalSeconds);
    
    return `${currentTime} ${progressBar} ${totalTime}`;
}

/**
 * Format seconds into MM:SS or HH:MM:SS
 * @param {number} seconds - Time in seconds
 * @returns {string} Formatted time string
 */
function formatTime(seconds) {
    if (!seconds || isNaN(seconds)) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    }
}

/**
 * Create a themed embed with consistent styling
 * @param {string} type - Embed type (playing, paused, error, etc.)
 * @param {object} options - Embed options
 * @returns {EmbedBuilder} Styled embed
 */
function createThemedEmbed(type, options = {}) {
    const embed = new EmbedBuilder();
    
    // Set color based on type
    const colorMap = {
        playing: COLORS.PLAYING,
        paused: COLORS.PAUSED,
        stopped: COLORS.STOPPED,
        loading: COLORS.LOADING,
        success: COLORS.SUCCESS,
        error: COLORS.ERROR,
        warning: COLORS.WARNING,
        info: COLORS.INFO,
        filter: COLORS.FILTER,
        loop: COLORS.LOOP,
        volume: COLORS.VOLUME,
        queue: COLORS.QUEUE,
        primary: COLORS.PRIMARY,
        secondary: COLORS.SECONDARY,
    };
    
    embed.setColor(colorMap[type] || COLORS.PRIMARY);
    
    // Add timestamp by default
    if (options.timestamp !== false) {
        embed.setTimestamp();
    }
    
    // Add footer with bot branding
    if (options.footer !== false) {
        const footerText = options.footerText || 'Rikai Music Bot';
        embed.setFooter({ 
            text: footerText,
            iconURL: options.footerIcon || 'https://cdn.discordapp.com/emojis/741605543046807626.png'
        });
    }
    
    return embed;
}

/**
 * Get emoji for volume level
 * @param {number} volume - Volume level (0-100)
 * @returns {string} Volume emoji
 */
function getVolumeEmoji(volume) {
    if (volume === 0) return EMOJIS.VOLUME_MUTE;
    if (volume <= 33) return EMOJIS.VOLUME_LOW;
    if (volume <= 66) return EMOJIS.VOLUME_MED;
    return EMOJIS.VOLUME_HIGH;
}

/**
 * Get emoji for queue position
 * @param {number} position - Position in queue (1-10)
 * @returns {string} Number emoji
 */
function getPositionEmoji(position) {
    if (position >= 1 && position <= 10) {
        return EMOJIS.NUMBERS[position - 1];
    }
    return `${position}.`;
}

/**
 * Create a fancy song title with formatting
 * @param {string} title - Song title
 * @param {string} url - Song URL
 * @param {boolean} isPlaying - Whether song is currently playing
 * @returns {string} Formatted title
 */
function formatSongTitle(title, url, isPlaying = false) {
    const maxLength = 50;
    const truncatedTitle = title.length > maxLength ? title.substring(0, maxLength) + '...' : title;
    const playingIndicator = isPlaying ? `${EMOJIS.AUDIO_WAVE} ` : '';
    
    return `${playingIndicator}**[${truncatedTitle}](${url})**`;
}

/**
 * Create a status indicator with emoji and text
 * @param {string} status - Status type
 * @param {string} text - Status text
 * @returns {string} Formatted status
 */
function createStatusIndicator(status, text) {
    const statusEmojis = {
        playing: EMOJIS.PLAY,
        paused: EMOJIS.PAUSE,
        stopped: EMOJIS.STOP,
        loading: EMOJIS.LOADING,
        success: EMOJIS.SUCCESS,
        error: EMOJIS.ERROR,
        warning: EMOJIS.WARNING,
    };
    
    const emoji = statusEmojis[status] || EMOJIS.INFO;
    return `${emoji} **${text}**`;
}

module.exports = {
    COLORS,
    EMOJIS,
    PROGRESS_CHARS,
    createProgressBar,
    createTimeProgressBar,
    formatTime,
    createThemedEmbed,
    getVolumeEmoji,
    getPositionEmoji,
    formatSongTitle,
    createStatusIndicator,
};
