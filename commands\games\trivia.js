const { ActionRow<PERSON>uilder, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, EmbedBuilder, MessageFlags } = require('discord.js');


const DIFFICULTIES = ['easy', 'medium', 'hard'];
const LETTERS = ['A', 'B', 'C', 'D'];

function shuffleArray(array) {
  return array.sort(() => Math.random() - 0.5);
}

function decodeHTML(str) {
  return str.replace(/&#(\d+);/g, (_, dec) => String.fromCharCode(dec))
    .replace(/&quot;/g, '"')
    .replace(/&amp;/g, '&')
    .replace(/&eacute;/g, 'é')
    .replace(/&uuml;/g, 'ü')
    .replace(/&rsquo;/g, '’');
}

module.exports = {
  name: 'trivia',
  aliases: ['quiz'],
  category: 'game',
  description: 'Play a trivia quiz',
  usePrefix: true,
  isEverywhere: false,

  async execute(message, args) {
    const difficultyArg = args[0]?.toLowerCase();
    const difficulty = DIFFICULTIES.includes(difficultyArg) ? difficultyArg : undefined;
    const timeoutSeconds = Math.min(parseInt(args[1]) || 30, 280);
    const timeout = timeoutSeconds * 1000;

    let score = 0;
    let health = 3;
    let stopped = false;

    const loadingMsg = await message.channel.send('🧠 Fetching trivia question...');

    while (health > 0 && !stopped) {
      const res = await fetch(`https://opentdb.com/api.php?amount=1&type=multiple${difficulty ? `&difficulty=${difficulty}` : ''}`);
      const data = await res.json();
      const trivia = data.results[0];

      const question = decodeHTML(trivia.question);
      const correctAnswer = decodeHTML(trivia.correct_answer);
      const allAnswers = shuffleArray([correctAnswer, ...trivia.incorrect_answers.map(decodeHTML)]);
      const correctIndex = allAnswers.indexOf(correctAnswer);
      const correctLetter = LETTERS[correctIndex];

      const embed = new EmbedBuilder()
        .setTitle('🧠 Trivia Quiz')
        .setColor(0x5865f2)
        .setDescription(`${question}\n\n${allAnswers.map((opt, i) => `**${LETTERS[i]})** ${opt}`).join('\n')}`)
        .setFooter({ text: `Player: ${message.author.username} | Health: ${'💖'.repeat(health)} | Score: ${score} | Time: ${timeoutSeconds}s | Difficulty: ${trivia.difficulty}` });

      const row = new ActionRowBuilder().addComponents(
        ...LETTERS.map(letter =>
          new ButtonBuilder()
            .setCustomId(letter)
            .setLabel(letter)
            .setStyle(ButtonStyle.Primary)
        ),
        new ButtonBuilder()
          .setCustomId('STOP')
          .setLabel('Stop')
          .setStyle(ButtonStyle.Danger)
      );

      const quizMsg = await loadingMsg.edit({ content: '', embeds: [embed], components: [row] });

      const collector = quizMsg.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: timeout
      });

      let answered = false;

      const countdownEmojis = ['3️⃣', '2️⃣', '1️⃣'];
      const timeoutTimers = countdownEmojis.map((emoji, i) =>
        setTimeout(async () => {
          if (!answered) {
            try {
              await quizMsg.react(emoji);
            } catch (e) {}
          }
        }, timeout - (3 - i) * 1000)
      );

      async function clearReactions() {
        try {
          const msg = await quizMsg.fetch();
          for (const emoji of countdownEmojis) {
            const reaction = msg.reactions.resolve(emoji);
            if (reaction) await reaction.remove();
          }
        } catch (e) {}
      }

      collector.on('collect', async interaction => {
        timeoutTimers.forEach(clearTimeout);
        answered = true;
        await clearReactions();
        collector.stop();

        if (interaction.customId === 'STOP') {
          stopped = true;
          await interaction.update({
            content: `🛑 Game stopped by ${message.author.username}. Final score: **${score}**`,
            embeds: [],
            components: []
          });
          return;
        }

        const picked = interaction.customId;
        const pickedAnswer = allAnswers[LETTERS.indexOf(picked)];

        if (picked === correctLetter) {
          score++;
          await interaction.update({
            content: `✅ Correct! The answer was **${correctLetter}) ${correctAnswer}**.`,
            embeds: [],
            components: []
          });
        } else {
          health--;
          if (health === 0) {
            await interaction.update({
              content: `💀 Out of health! Correct answer: **${correctLetter}) ${correctAnswer}**`,
              embeds: [],
              components: []
            });
          } else {
            await interaction.reply({
              content: `❌ Incorrect! Health left: ${'💖'.repeat(health)}. Correct answer: **${correctLetter}) ${correctAnswer}**`,
              flags: MessageFlags.Ephemeral
            });
          }
        }
      });

      await new Promise(resolve => {
        collector.on('end', async () => {
          timeoutTimers.forEach(clearTimeout);
          if (!answered && !stopped) {
            health--;
            await quizMsg.edit({
              content: `⏰ Time's up! The correct answer was **${correctLetter}) ${correctAnswer}**. Health left: ${'💖'.repeat(health)}`,
              embeds: [],
              components: []
            });
          }
          await clearReactions();
          resolve();
        });
      });
    }

    if (!stopped) {
      await message.channel.send(`🎉 Quiz over, ${message.author.username}! Final score: **${score}**`);
    }
  }
};
