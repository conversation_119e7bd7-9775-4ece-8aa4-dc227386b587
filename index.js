const { Client, Collection, GatewayIntentBits, Partials, } = require('discord.js');
const { Player } = require('discord-player');
const { DefaultExtractors } = require('@discord-player/extractor');
const {YoutubeiExtractor} = require('discord-player-youtubei');
require('dotenv').config();
const {DeezerExtractor, NodeDecryptor} = require('discord-player-deezer');
const { SoundcloudExtractor } = require("discord-player-soundcloud");
const {YtDlpExtractor} = require('./discord-player-ytdlp');
const { Log } = require('youtubei.js');
const path = require('path');
const fs = require('fs');
Log.setLevel(Log.Level.NONE)
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildWebhooks,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.DirectMessages,
        GatewayIntentBits.GuildPresences,
        
    ],
    allowedMentions: {
        parse: ['users', 'roles'],
        repliedUser: false
    },
    partials : [
        Partials.Channel,
        Partials.Message,
        Partials.User,
        Partials.GuildMember,
        Partials.Reaction
    ]
});

// Initialize collections
client.slashCommands = new Collection();
client.textCommands = new Collection();
client.buttons = new Collection();
client.selectMenus = new Collection();
client.contextMenus = new Collection();
client.modals = new Collection();
global.client = client;
async function initialize() {
    
    // Start Express server
    const { startServer } = require('./web/server');
    startServer();
    
    try {
        // Initialize player
        const player = new Player(client);




        // Resolve yt-dlp path - try multiple methods for PM2 compatibility
        let ytdlpPath = path.resolve(__dirname, 'bin', 'yt-dlp.exe');

        // If primary path doesn't exist, try alternative paths
        if (!fs.existsSync(ytdlpPath)) {
            // Try using process.cwd() (working directory)
            const altPath = path.resolve(process.cwd(), 'bin', 'yt-dlp.exe');

            if (fs.existsSync(altPath)) {
                ytdlpPath = altPath;
            } else {
                throw new Error(`yt-dlp binary not found at: ${ytdlpPath} or ${altPath}`);
            }
        }

        await player.extractors.register(YtDlpExtractor, {
            ytdlpPath: ytdlpPath,
            enableYouTubeSearch: true,
            enableDirectUrls: true,
            streamQuality: 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio', //slow af
            youtubeiOptions: {
                cookies: null,
                client: "WEB_EMBEDDED"
            }
        });

        // Load default extractors as fallback (lower priority)
        // await player.extractors.loadMulti(DefaultExtractors);
        // await player.extractors.register(YoutubeiExtractor, {
        //     generateWithPoToken:true,
        //     cookie:process.env.cookie,
        //     streamOptions: {
        //         useClient: "WEB_EMBEDDED",
        //     }

        // })
        // await player.extractors.register(DeezerExtractor, {
        //   /** extractor options goes here */
        //     decryptionKey:process.env.key,
        //     arl: process.env.arl,
        //     decryptor: NodeDecryptor,
        // })
        // await player.extractors.register(SoundcloudExtractor, {
        //   /** extractor options goes here */
        // })

        // Load all handlers
        require('./loaders/discordEvents')(client);
        require('./loaders/playerEvents')(player);
        require('./loaders/slashCommands')(client);
        require('./loaders/textCommands')(client);
        require('./loaders/buttons')(client);
        require('./loaders/selectMenus')(client);
        require('./loaders/contextMenus')(client);
        require('./loaders/modals')(client);

        // Initialize yt-dlp auto-updater plugin
        try {
            require('./plugin/javascript/ytdlp-updater');
            console.log('✅ yt-dlp auto-updater plugin loaded');
        } catch (error) {
            console.warn('⚠️ Failed to load yt-dlp updater plugin:', error.message);
        }

        // Start bot
        await client.login(process.env.token);
        console.log(`✅ Logged in as ${client.user.tag}!`);
    } catch (error) {
        console.error('❌ Initialization failed:', error);
        
    }
}

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

process.on("SIGINT", () => {
    console.log("SIGINT received. Shutting down gracefully.");
    client.destroy(); // disconnect from Discord
    process.exit(0);
  });
  
  process.on("SIGTERM", () => {
    console.log("SIGTERM received. Shutting down gracefully.");
    client.destroy(); // disconnect from Discord
    process.exit(0);
  });
  
initialize();
