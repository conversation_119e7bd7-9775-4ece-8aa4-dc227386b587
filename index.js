const { Client, Collection, GatewayIntentBits, Partials, } = require('discord.js');
const { Player } = require('discord-player');
const { DefaultExtractors } = require('@discord-player/extractor');
const {YoutubeiExtractor} = require('discord-player-youtubei');
require('dotenv').config();
const {DeezerExtractor, NodeDecryptor} = require('discord-player-deezer');
const { SoundcloudExtractor } = require("discord-player-soundcloud");
const {YtDlpExtractor} = require('./discord-player-ytdlp');
const { Log } = require('youtubei.js');
const path = require('path');
const fs = require('fs');
Log.setLevel(Log.Level.NONE)
const client = new Client({
    intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildWebhooks,
        GatewayIntentBits.GuildMessageReactions,
        GatewayIntentBits.GuildMembers,
        GatewayIntentBits.DirectMessages,
        GatewayIntentBits.GuildPresences,
        
    ],
    allowedMentions: {
        parse: ['users', 'roles'],
        repliedUser: false
    },
    partials : [
        Partials.Channel,
        Partials.Message,
        Partials.User,
        Partials.GuildMember,
        Partials.Reaction
    ]
});

// Initialize collections
client.slashCommands = new Collection();
client.textCommands = new Collection();
client.buttons = new Collection();
client.selectMenus = new Collection();
client.contextMenus = new Collection();
client.modals = new Collection();

// Memory management and cleanup tracking
client.activeTimers = new Set();
client.activeIntervals = new Set();
client.activeCollectors = new Set();
client.activeProcesses = new Set();

global.client = client;
async function initialize() {
    
    // Start Express server
    const { startServer } = require('./web/server');
    startServer();
    
    try {
        // Initialize player
        const player = new Player(client);




        // Resolve yt-dlp path - try multiple methods for PM2 compatibility
        let ytdlpPath = path.resolve(__dirname, 'bin', 'yt-dlp.exe');

        // If primary path doesn't exist, try alternative paths
        if (!fs.existsSync(ytdlpPath)) {
            // Try using process.cwd() (working directory)
            const altPath = path.resolve(process.cwd(), 'bin', 'yt-dlp.exe');

            if (fs.existsSync(altPath)) {
                ytdlpPath = altPath;
            } else {
                throw new Error(`yt-dlp binary not found at: ${ytdlpPath} or ${altPath}`);
            }
        }

        await player.extractors.register(YtDlpExtractor, {
            ytdlpPath: ytdlpPath,
            enableYouTubeSearch: true,
            enableDirectUrls: true,
            streamQuality: 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio', //slow af
            youtubeiOptions: {
                cookies: null,
                client: "WEB_EMBEDDED"
            }
        });

        // Load default extractors as fallback (lower priority)
        // await player.extractors.loadMulti(DefaultExtractors);
        // await player.extractors.register(YoutubeiExtractor, {
        //     generateWithPoToken:true,
        //     cookie:process.env.cookie,
        //     streamOptions: {
        //         useClient: "WEB_EMBEDDED",
        //     }

        // })
        // await player.extractors.register(DeezerExtractor, {
        //   /** extractor options goes here */
        //     decryptionKey:process.env.key,
        //     arl: process.env.arl,
        //     decryptor: NodeDecryptor,
        // })
        // await player.extractors.register(SoundcloudExtractor, {
        //   /** extractor options goes here */
        // })

        // Load all handlers
        require('./loaders/discordEvents')(client);
        require('./loaders/playerEvents')(player);
        require('./loaders/slashCommands')(client);
        require('./loaders/textCommands')(client);
        require('./loaders/buttons')(client);
        require('./loaders/selectMenus')(client);
        require('./loaders/contextMenus')(client);
        require('./loaders/modals')(client);

        // Initialize yt-dlp auto-updater plugin
        try {
            require('./plugin/javascript/ytdlp-updater');
            console.log('✅ yt-dlp auto-updater plugin loaded');
        } catch (error) {
            console.warn('⚠️ Failed to load yt-dlp updater plugin:', error.message);
        }

        // Start memory monitoring
        const { memoryMonitor } = require('./utils/memoryMonitor');
        memoryMonitor.startMonitoring(60000); // Check every minute

        // Start bot
        await client.login(process.env.token);
        console.log(`✅ Logged in as ${client.user.tag}!`);

        // Log initial memory stats
        setTimeout(() => {
            const stats = memoryMonitor.getMemoryStats();
            console.log('📊 Initial Memory Stats:', stats.formatted);
        }, 5000);
    } catch (error) {
        console.error('❌ Initialization failed:', error);
        
    }
}



// Enhanced graceful shutdown with memory cleanup
async function gracefulShutdown(signal) {
    console.log(`${signal} received. Shutting down gracefully...`);

    try {
        // Clear all active timers and intervals
        console.log('🧹 Cleaning up timers and intervals...');
        client.activeTimers.forEach(timer => clearTimeout(timer));
        client.activeIntervals.forEach(interval => clearInterval(interval));

        // Stop all active collectors
        console.log('🧹 Cleaning up collectors...');
        client.activeCollectors.forEach(collector => {
            if (collector && typeof collector.stop === 'function') {
                collector.stop();
            }
        });

        // Clean up active processes
        console.log('🧹 Cleaning up processes...');
        client.activeProcesses.forEach(process => {
            if (process && typeof process.kill === 'function') {
                process.kill('SIGTERM');
            }
        });

        // Stop yt-dlp updater
        try {
            const { updater } = require('./plugin/javascript/ytdlp-updater');
            if (updater && typeof updater.stopAutoUpdateTimer === 'function') {
                updater.stopAutoUpdateTimer();
            }
        } catch (error) {
            console.warn('⚠️ Could not stop yt-dlp updater:', error.message);
        }

        // Stop memory monitor
        try {
            const { memoryMonitor } = require('./utils/memoryMonitor');
            if (memoryMonitor && typeof memoryMonitor.stopMonitoring === 'function') {
                memoryMonitor.stopMonitoring();
            }
        } catch (error) {
            console.warn('⚠️ Could not stop memory monitor:', error.message);
        }

        // Clean up temporary files
        console.log('🧹 Cleaning up temporary files...');
        const fs = require('fs');
        const path = require('path');
        const tempDir = path.join(__dirname, 'web', 'temp');
        if (fs.existsSync(tempDir)) {
            const files = fs.readdirSync(tempDir);
            files.forEach(file => {
                try {
                    fs.unlinkSync(path.join(tempDir, file));
                } catch (error) {
                    console.warn(`⚠️ Could not delete temp file ${file}:`, error.message);
                }
            });
        }

        // Force garbage collection if available
        if (global.gc) {
            console.log('🧹 Running garbage collection...');
            global.gc();
        }

        // Disconnect from Discord
        console.log('🔌 Disconnecting from Discord...');
        await client.destroy();

        console.log('✅ Graceful shutdown completed');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error during graceful shutdown:', error);
        process.exit(1);
    }
}

process.on("SIGINT", () => gracefulShutdown("SIGINT"));
process.on("SIGTERM", () => gracefulShutdown("SIGTERM"));

// Handle uncaught exceptions and rejections with cleanup
process.on('uncaughtException', async (error) => {
    console.error('❌ Uncaught Exception:', error);
    await gracefulShutdown('UNCAUGHT_EXCEPTION');
});

process.on('unhandledRejection', async (reason, promise) => {
    console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
    // Don't exit on unhandled rejection, just log it
});
  
initialize();
