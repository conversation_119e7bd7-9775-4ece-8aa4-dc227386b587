const { ContextMenuCommandBuilder, ApplicationCommandType, EmbedBuilder, MessageFlags, time } = require('discord.js');

module.exports = {
    data: new ContextMenuCommandBuilder()
        .setName('User Info')
        .setType(ApplicationCommandType.User),

    async execute(client, interaction) {
        const user = interaction.targetUser;
        const member = interaction.guild.members.cache.get(user.id) || await interaction.guild.members.fetch(user.id).catch(() => null);
        const fetchedUser = await user.fetch().catch(() => null);

        // User Badges
        const badges = fetchedUser?.flags?.toArray().join(", ") || "None";

        // Presence (Status & Activities)
        const presence = member?.presence?.status || "Offline";
        const activities = member?.presence?.activities || [];
        let activityText = activities.length ? activities.map(activity => {
            let details = `**${activity.type}** ${activity.name}`;
            if (activity.state) details += ` - ${activity.state}`;
            if (activity.details) details += `\n> **Details:** ${activity.details}`;
            if (activity.assets?.largeText) details += `\n> **Hover Text:** ${activity.assets.largeText}`;
            return details;
        }).join("\n\n") : "None";

        // User Avatar & Banner
        const avatarURL = user.displayAvatarURL({ dynamic: true, size: 1024 });
        const bannerUrl = fetchedUser?.banner ? fetchedUser.bannerURL({ dynamic: true, size: 1024 }) : null;

        // Accent Color (if no banner)
        const accentColor = fetchedUser?.accentColor ? `#${fetchedUser.accentColor.toString(16).padStart(6, "0").toUpperCase()}` : "None";

        // Nitro Status (Animated Avatar/Banner)
        const hasNitro = user.avatar?.startsWith("a_") || fetchedUser?.banner;

        // Additional User Info
        const globalName = fetchedUser?.globalName || "None";
        const locale = fetchedUser?.locale || "Unknown";
        const isMFAEnabled = fetchedUser?.mfaEnabled ? "Yes" : "No";

        // Timeout Status
        const isTimedOut = member?.communicationDisabledUntilTimestamp
            ? `Until <t:${Math.floor(member.communicationDisabledUntilTimestamp / 1000)}:F>`
            : "No";

        // Boosting Status
        const boostingSince = member?.premiumSinceTimestamp
            ? `<t:${Math.floor(member.premiumSinceTimestamp / 1000)}:F>`
            : "No";

        // Account Age
        const accountAge = `<t:${Math.floor(user.createdTimestamp / 1000)}:F> (${time(Math.floor(user.createdTimestamp / 1000), "R")})`;

        // Role List (Mentioned)
        const roleList = member?.roles.cache.size > 1
            ? member.roles.cache.filter(r => r.id !== interaction.guild.id).map(r => r).join(", ")
            : "None";

        // Embed
        const embed = new EmbedBuilder()
            .setColor(member?.displayHexColor || fetchedUser?.accentColor || 0x2F3136)
            .setThumbnail(avatarURL)
            .setTitle(`👤 User Info: ${user.username}`)
            .addFields(
                { name: "Username", value: user.username, inline: true },
                { name: "Global Name", value: globalName, inline: true },
                { name: "Tag", value: user.tag, inline: true },
                { name: "ID", value: user.id, inline: false },
                { name: "Bot?", value: user.bot ? "Yes" : "No", inline: true },
                { name: "Badges", value: badges, inline: false },
                { name: "Nitro?", value: hasNitro ? "Yes" : "No", inline: true },
                { name: "Accent Color", value: accentColor, inline: true },
                { name: "Locale", value: locale, inline: true },
                { name: "MFA Enabled?", value: isMFAEnabled, inline: true },
                { name: "Account Age", value: accountAge, inline: false },
                ...(member ? [
                    { name: "Joined Server", value: `<t:${Math.floor(member.joinedTimestamp / 1000)}:F> (${time(Math.floor(member.joinedTimestamp / 1000), "R")})`, inline: false },
                    { name: "Roles", value: roleList, inline: false },
                    { name: "Highest Role", value: member.roles.highest.name, inline: true },
                    { name: "Boosting?", value: boostingSince, inline: true },
                    { name: "Timeout?", value: isTimedOut, inline: true },
                    { name: "Presence", value: `Status: **${presence}**`, inline: false },
                    { name: "Activities", value: activityText, inline: false }
                ] : [])
            )
            .setFooter({ text: `Requested by ${interaction.user.tag}`, iconURL: interaction.user.displayAvatarURL({ dynamic: true }) })
            .setTimestamp();

        if (bannerUrl) embed.setImage(bannerUrl); // Add banner if exists

        await interaction.reply({ embeds: [embed], flags: MessageFlags.Ephemeral });
    }
};
