const puppeteer = require("puppeteer");
const cheerio = require("cheerio");
const {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ActionRowBuilder,
  ButtonBuilder,
  ButtonStyle,
} = require("discord.js");

function convertLinks($, el) {
  const cloned = $(el).clone();

  cloned.find("a").each((_, a) => {
    const $a = $(a);
    const text = $a.text();
    const href = $a.attr("href");
    if (href && text) {
      const fullUrl = "https://www.urbandictionary.com" + href;
      $a.replaceWith(`[${text}](${fullUrl})`);
    }
  });

  return cloned.text().trim();
}

async function fetchDefinitions(query, page = 1) {
  const url = `https://www.urbandictionary.com/define.php?term=${query}&page=${page}`;
  const browser = await puppeteer.launch({ headless: "new" });
  const pageObj = await browser.newPage();
  await pageObj.goto(url, { waitUntil: "networkidle2" });

  const html = await pageObj.content();
  const $ = cheerio.load(html);
  const definitions = [];

  $("div.p-5.md\\:p-8").each((i, el) => {
    const word = $(el).find("a.word").first().text().trim();
    const meaning = convertLinks($, $(el).find("div.meaning").first());
    const example =
      convertLinks($, $(el).find("div.example").first()) || "No example provided.";

    const contributorEl = $(el).find("div.contributor a").first();
    const contributorName = contributorEl.text().trim();
    const contributorHref = contributorEl.attr("href");

    const contributorDiv = $(el).find("div.contributor");
    const dateNode = contributorDiv
      .contents()
      .filter((i, el) => el.type === "text" && el.prev && el.prev.name === "a")
      .first();
    const date = dateNode ? dateNode.text().trim() : "Unknown date";

    const contributor = contributorHref
      ? `[${contributorName}](https://www.urbandictionary.com${contributorHref}) • ${date}`
      : `${contributorName} • ${date}`;

    const upvotes =
      $(el).find('button[data-x-bind="thumbUp"] span.text-xs').text().trim() || "0";
    const downvotes =
      $(el).find('button[data-x-bind="thumbDown"] span.text-xs').text().trim() || "0";

    if (word && meaning) {
      definitions.push({
        word,
        meaning,
        example,
        contributor,
        upvotes,
        downvotes,
      });
    }
  });

  await browser.close();
  return definitions;
}

module.exports = {
  name: "urban",
  aliases: ["ub"],
  category: "fun",
  description: "Search for a word in Urban Dictionary",
  usePrefix: true,

  async execute(message, args) {
    if (!args.length) {
      return message.reply("Please enter a word to search. Example: `!urban cooking`");
    }

    const query = encodeURIComponent(args.join(" "));
    let currentPage = 1;
    let definitions = await fetchDefinitions(query, currentPage);

    if (definitions.length === 0) {
      return message.reply(`No results found for "${args.join(" ")}".`);
    }

    let index = 0;

    const getEmbed = (def, idx, pageNum) =>
      new EmbedBuilder()
        .setColor("#EFFF00")
        .setTitle(`${def.word} (${idx + 1}/${definitions.length})`)
        .setURL(`https://www.urbandictionary.com/define.php?term=${query}&page=${pageNum}`)
        .setDescription(`📖 **Definition:**\n${def.meaning}`)
        .addFields(
          { name: "📌 Example", value: def.example },
          { name: "👤 Contributor", value: def.contributor }
        )
        .setFooter({
          text: `👍 ${def.upvotes} | 👎 ${def.downvotes} • Urban Dictionary • Page ${pageNum}, Definition ${idx + 1}`,
        });

    const getActionRow = (idx, page) =>
      new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId("prevDef")
          .setLabel("⬅️ Previous")
          .setStyle(ButtonStyle.Primary)
          .setDisabled(idx === 0),
        new ButtonBuilder()
          .setCustomId("nextDef")
          .setLabel("Next ➡️")
          .setStyle(ButtonStyle.Primary)
          .setDisabled(idx >= definitions.length - 1),
        new ButtonBuilder()
          .setCustomId("prevPage")
          .setLabel("⬅️ Prev Page")
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === 1),
        new ButtonBuilder()
          .setCustomId("nextPage")
          .setLabel("Next Page ➡️")
          .setStyle(ButtonStyle.Secondary)
      );

    const sentMessage = await message.channel.send({
      embeds: [getEmbed(definitions[index], index, currentPage)],
      components: [getActionRow(index, currentPage)],
    });

    const filter = (i) => i.user.id === message.author.id;
    const collector = sentMessage.createMessageComponentCollector({
      filter,
      time: 120000,
    });

    collector.on("collect", async (interaction) => {
      await interaction.deferUpdate();

      if (interaction.customId === "nextDef" && index < definitions.length - 1) {
        index++;
      } else if (interaction.customId === "prevDef" && index > 0) {
        index--;
      } else if (interaction.customId === "nextPage") {
        currentPage++;
        index = 0;
        definitions = await fetchDefinitions(query, currentPage);
        if (definitions.length === 0) {
          currentPage--;
          definitions = await fetchDefinitions(query, currentPage);
          return;
        }
      } else if (interaction.customId === "prevPage" && currentPage > 1) {
        currentPage--;
        index = 0;
        definitions = await fetchDefinitions(query, currentPage);
      }

      await sentMessage.edit({
        embeds: [getEmbed(definitions[index], index, currentPage)],
        components: [getActionRow(index, currentPage)],
      });
    });

    collector.on("end", () => {
      sentMessage.edit({ components: [] }).catch(() => {});
    });
  },
};
