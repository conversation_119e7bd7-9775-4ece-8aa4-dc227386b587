const axios = require("axios");
const { Embed<PERSON>uilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");

module.exports = {
    name: "anime",
    aliases: ["ani"],
    category: "anime",
    description: "Cari info anime dari MyAnimeList.",
    usePrefix: true,
    isEverywhere: false,

    async execute(message, args) {
        const query = args.join(" ");
        if (!query) {
            return message.reply("❌ Masukkan judul anime yang ingin dicari. Contoh: `!anime One Piece`");
        }

        try {
            const response = await axios.get(`https://api.jikan.moe/v4/anime?q=${encodeURIComponent(query)}&limit=10&page=1`);
            const animeList = response.data.data;

            if (!animeList || animeList.length === 0) {
                return message.reply(`❌ Anime dengan judul **"${query}"** tidak ditemukan.`);
            }

            let currentIndex = 0;

            const fetchAnimeDetails = async (animeId) => {
                const res = await axios.get(`https://api.jikan.moe/v4/anime/${animeId}/full`);
                return res.data.data;
            };

            const createEmbed = async (index) => {
                const anime = await fetchAnimeDetails(animeList[index].mal_id);

                return new EmbedBuilder()
                    .setColor("#0099ff")
                    .setTitle(anime.title)
                    .setURL(`https://myanimelist.net/anime/${anime.mal_id}`)
                    .setDescription(anime.synopsis ? anime.synopsis.slice(0, 2000) + "..." : "No synopsis available.")
                    .setThumbnail(anime.images.jpg.large_image_url)
                    .addFields(
                        { name: "📌 Japanese", value: anime.title_japanese || "Unknown", inline: true },
                        { name: "📌 Synonyms", value: anime.title_synonyms.length > 0 ? anime.title_synonyms.join(", ") : "None", inline: true },
                        { name: "📌 Type", value: anime.type || "Unknown", inline: true },
                        { name: "📌 Episodes", value: anime.episodes ? anime.episodes.toString() : "Unknown", inline: true },
                        { name: "📌 Duration", value: anime.duration || "Unknown", inline: true },
                        { name: "📌 Status", value: anime.status || "Unknown", inline: true },
                        { name: "📌 Score", value: anime.score ? anime.score.toString() : "N/A", inline: true },
                        { name: "📌 Rating", value: anime.rating || "Unrated", inline: true },
                        { name: "📌 Genre", value: anime.genres.length > 0 ? anime.genres.map(g => g.name).join(", ") : "Unknown", inline: false },
                        { name: "📌 Studios", value: anime.studios.length > 0 ? anime.studios.map(s => s.name).join(", ") : "Unknown", inline: false },
                        { name: "📌 Aired", value: anime.aired.string || "Unknown", inline: false },
                        { name: "📌 Source", value: anime.source || "Unknown", inline: true }
                    )
                    .setFooter({ text: `Anime ${index + 1} dari ${animeList.length}` });
            };

            const createButtonRow = (index) => {
                return new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId("prev_anime")
                        .setLabel("◀ Prev")
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(index === 0),
                    new ButtonBuilder()
                        .setCustomId("next_anime")
                        .setLabel("Next ▶")
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(index === animeList.length - 1),
                    new ButtonBuilder()
                        .setLabel("View on MAL")
                        .setStyle(ButtonStyle.Link)
                        .setURL(`https://myanimelist.net/anime/${animeList[index].mal_id}`)
                );
            };

            let embed = await createEmbed(currentIndex);
            let buttons = createButtonRow(currentIndex);

            const sentMessage = await message.channel.send({
                embeds: [embed],
                components: [buttons]
            });

            const collector = sentMessage.createMessageComponentCollector({
                filter: i => i.user.id === message.author.id,
                time: 60000
            });

            collector.on("collect", async (interaction) => {
                await interaction.deferUpdate();

                if (interaction.customId === "prev_anime") {
                    currentIndex--;
                } else if (interaction.customId === "next_anime") {
                    currentIndex++;
                }

                const updatedEmbed = await createEmbed(currentIndex);
                const updatedButtons = createButtonRow(currentIndex);

                await sentMessage.edit({
                    embeds: [updatedEmbed],
                    components: [updatedButtons]
                });
            });

            collector.on("end", () => {
                sentMessage.edit({ components: [] }).catch(() => {});
            });

        } catch (error) {
            console.error("Error fetching anime data:", error);
            return message.reply("❌ Terjadi kesalahan saat mengambil data anime. Silakan coba lagi nanti.");
        }
    }
};
