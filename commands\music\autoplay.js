const { createThemedEmbed, createStatusIndicator, EMOJIS } = require('../../utils/embedTheme');

module.exports = {
    name: 'autoplay',
    description: 'Toggle autoplay to automatically add related songs when the queue ends',
    aliases: ['ap', 'auto'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        // Get the play command to access guildQueues
        const playCommand = require('./play.js');
        const guildQueues = playCommand.guildQueues;

        if (!guildQueues) {
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Music System Error`)
                .setDescription(`${createStatusIndicator('error', 'Music system not available')}`);
            return message.reply({ embeds: [errorEmbed] });
        }

        const serverQueue = guildQueues.get(message.guild.id);
        if (!serverQueue) {
            const noQueueEmbed = createThemedEmbed('warning')
                .setTitle(`${EMOJIS.WARNING} No Active Queue`)
                .setDescription(`${createStatusIndicator('warning', 'No music is currently playing')}\n\n${EMOJIS.INFO} Start playing music first with \`!play\``)
                .addFields({
                    name: `${EMOJIS.SPARKLES} About Autoplay`,
                    value: `${EMOJIS.MUSIC_NOTE} Automatically adds related songs when the queue ends\n${EMOJIS.LIGHTNING} Keeps the music going without manual input\n${EMOJIS.HEART} Based on YouTube's recommendation system`,
                    inline: false
                });
            return message.reply({ embeds: [noQueueEmbed] });
        }

        // Check if user provided a specific action
        const action = args[0]?.toLowerCase();
        
        if (action === 'on' || action === 'enable' || action === 'true') {
            serverQueue.autoplay = true;
        } else if (action === 'off' || action === 'disable' || action === 'false') {
            serverQueue.autoplay = false;
        } else {
            // Toggle autoplay
            serverQueue.autoplay = !serverQueue.autoplay;
        }

        // Create status embed
        const statusEmbed = createThemedEmbed(serverQueue.autoplay ? 'success' : 'info')
            .setTitle(`${EMOJIS.SPARKLES} Autoplay ${serverQueue.autoplay ? 'Enabled' : 'Disabled'}`)
            .setDescription(`${createStatusIndicator(serverQueue.autoplay ? 'success' : 'info', 
                serverQueue.autoplay 
                    ? 'Related songs will be automatically added when the queue ends'
                    : 'Music will stop when the queue ends'
            )}`)
            .addFields(
                {
                    name: `${EMOJIS.INFO} Current Status`,
                    value: `${serverQueue.autoplay ? EMOJIS.SUCCESS : EMOJIS.ERROR} Autoplay is **${serverQueue.autoplay ? 'ON' : 'OFF'}**`,
                    inline: true
                },
                {
                    name: `${EMOJIS.MUSIC_NOTE} Queue Status`,
                    value: `${EMOJIS.QUEUE} ${serverQueue.songs.length} songs in queue`,
                    inline: true
                }
            );

        if (serverQueue.autoplay) {
            statusEmbed.addFields({
                name: `${EMOJIS.SPARKLES} How It Works`,
                value: `${EMOJIS.LIGHTNING} When the queue ends, I'll automatically add 2-3 related songs\n${EMOJIS.HEART} Based on the last played song's recommendations\n${EMOJIS.FILTER} Avoids recently played songs to prevent repetition`,
                inline: false
            });
        } else {
            statusEmbed.addFields({
                name: `${EMOJIS.INFO} To Re-enable`,
                value: `${EMOJIS.PLAY} Use \`!autoplay on\` or \`!autoplay\` to toggle`,
                inline: false
            });
        }

        await message.reply({ embeds: [statusEmbed] });
    },
};
