const {customCommandDB} = require('../../database/manager')

module.exports = {
    name: 'addcommand',
    aliases: ['customcmd'],
    category: 'custom',
    description: 'Add a custom command.\nFormat: `!addcommand <name> <response>`\nAdd `--noprefix` for a command without a prefix.\nAdd `--isEverywhere` to trigger it anywhere.',
    usePrefix: true,
    isEverywhere: false,

    async execute(message, args) {
        const useNoPrefix = args.includes('--noprefix');
        const isEverywhere = args.includes('--isEverywhere');

        // Remove flags from args
        args = args.filter(arg => arg !== '--noprefix' && arg !== '--isEverywhere');

        const commandName = args.shift()?.toLowerCase();
        const responseText = args.join(" ");

        if (!commandName || !responseText) {
            return message.reply('❌ Incorrect format! Use: `!addcommand <name> <response>`\nAdd `--noprefix` if you don\'t want to use a prefix.\nAdd `--isEverywhere` to trigger it anywhere.');
        }

        const existingCommand = message.client.textCommands.get(commandName) || 
            message.client.textCommands.find(cmd => cmd.aliases?.includes(commandName));

        if (existingCommand) {
            return message.reply(`❌ Cannot add command \`${commandName}\` because it is already used by a built-in command.`);
        }

        await customCommandDB.set(`custom_${commandName}`, { response: responseText, usePrefix: !useNoPrefix, isEverywhere });

        message.reply(`✅ Command **${commandName}** has been added!\nUse: \`${useNoPrefix ? commandName : `!${commandName}`}\`${isEverywhere ? ' (🌍 Everywhere)' : ''}`);
    }
};
