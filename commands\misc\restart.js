const { exec } = require("child_process");

module.exports = {
    name: "restart",
    aliases: ["rst"],
    category: "misc",
    description: "Restart the bot",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        try {
            // <PERSON>iksa apakah pengguna memiliki izin
            if (message.author.id !== process.env.tama) {
                return message.reply("You don't have the power to control me!");
            }

            // <PERSON>rim pesan konfirmasi sebelum restart
            await message.reply("Restarting... Please wait for 5 seconds.");

            // Restart bot menggunakan PM2 dari instalasi lokal
            exec("npx pm2 restart rikai", (error, stdout, stderr) => {
                if (error) {
                    console.error(`Error during restart: ${error.message}`);
                    return message.channel.send("Failed to restart the bot. Please try again later.");
                }
                if (stderr) {
                    console.error(`Stderr: ${stderr}`);
                }
                console.log(`Stdout: ${stdout}`);
            });
        } catch (err) {
            console.error(`Failed to restart the bot: ${err.message}`);
        }
    },
};
