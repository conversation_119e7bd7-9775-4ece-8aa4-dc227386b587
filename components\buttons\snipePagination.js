const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle, MessageFlags } = require('discord.js');
const { snipeDB } = require('../../database/manager')

module.exports = {
    customId: 'snipeuser=',

    async execute(client, interaction, id) {
        const [action, sessionId] = id.split(':');

        const session = await snipeDB.get(sessionId);
        if (!session) {
            return interaction.reply({
                content: '⚠️ Session expired or not found.',
                flags: MessageFlags.Ephemeral,
            });
        }

        let index = session.index;

        if (action === 'next') index++;
        else if (action === 'prev') index--;

        index = Math.max(0, Math.min(session.logs.length - 1, index));

        await snipeDB.set(sessionId, { ...session, index });

        const embed = buildSnipeEmbed(session.logs[index], index, session.logs.length);
        const row = buildPaginationRow(index, session.logs.length, sessionId);

        return interaction.update({
            embeds: [embed],
            components: [row],
        });
    },
};

function buildSnipeEmbed(log, index, total) {
    const embed = new EmbedBuilder()
        .setColor('#FF7F7F')
        .setAuthor({ name: log.authorTag })
        .setTitle(`🕵️ Deleted Message #${index + 1}`)
        .setFooter({ text: `Deleted at • ${new Date(log.deletedAt).toLocaleString()} | ${index + 1} / ${total}` });

    embed.setDescription(log.content?.slice(0, 2048) || '*[No text content]*');

    if (log.attachments?.length > 0) {
        embed.addFields({
            name: '📎 Attachment(s)',
            value: log.attachments.map((url, i) => `[Attachment ${i + 1}](${url})`).join('\n'),
        });

        const imageAttachment = log.attachments.find(url => {
            const baseUrl = url.split('?')[0];
            return /\.(jpeg|jpg|gif|png|webp)$/i.test(baseUrl);
        });
        
        if (imageAttachment) {
            embed.setImage(imageAttachment);
        }
    }

    if (log.embeds?.length > 0) {
        const embedDetails = log.embeds.map((e, idx) => {
            return `**Embed ${idx + 1}**\n${e.title ? `• Title: ${e.title}\n` : ''}${e.description ? `• Desc: ${e.description.slice(0, 100)}\n` : ''}${e.url ? `• URL: ${e.url}` : ''}`;
        }).join('\n\n');
        embed.addFields({ name: '🖼️ Embedded Content', value: embedDetails.slice(0, 1024) });
    }

    return embed;
}

function buildPaginationRow(index, total, sessionId) {
    return new ActionRowBuilder().addComponents(
        new ButtonBuilder()
            .setCustomId(`snipeuser=prev:${sessionId}`)
            .setLabel('◀️ Previous')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(index === 0),
        new ButtonBuilder()
            .setCustomId(`snipeuser=next:${sessionId}`)
            .setLabel('Next ▶️')
            .setStyle(ButtonStyle.Secondary)
            .setDisabled(index >= total - 1)
    );
}