/**
 * Shuffles an array using the <PERSON><PERSON><PERSON> algorithm.
 *
 * @param {Array} array - The array to shuffle.
 * @returns {Array} - A new shuffled array.
 */
function shuffleArray(array) {
    const arr = [...array];
    for (let i = arr.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [arr[i], arr[j]] = [arr[j], arr[i]];
    }
    return arr;
}

module.exports = { shuffleArray };
