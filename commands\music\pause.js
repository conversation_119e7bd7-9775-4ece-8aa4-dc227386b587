const { AudioPlayerStatus } = require('@discordjs/voice');

const {
    createThemedEmbed,
    formatSongTitle,
    createStatusIndicator,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

module.exports = {
    name: 'pause',
    description: 'Pause the currently playing music',
    aliases: ['pa'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || !serverQueue.playing || !serverQueue.currentSong) {
            const noMusicEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Music Playing`)
                .setDescription(`${createStatusIndicator('error', 'There is no music currently playing!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to start playing music.`);
            return message.reply({ embeds: [noMusicEmbed] });
        }

        // Check if already paused
        if (serverQueue.player.state.status === AudioPlayerStatus.Paused) {
            const alreadyPausedEmbed = createThemedEmbed('warning')
                .setTitle(`${EMOJIS.PAUSE} Already Paused`)
                .setDescription(`${createStatusIndicator('warning', 'The music is already paused!')}\n\n${EMOJIS.PLAY} Use \`resume\` to continue playing.`);
            return message.reply({ embeds: [alreadyPausedEmbed] });
        }

        // Pause the player
        const success = serverQueue.player.pause(true);

        if (success) {
            const pauseEmbed = createThemedEmbed('paused')
                .setTitle(`${EMOJIS.PAUSE} Music Paused`)
                .setDescription(formatSongTitle(serverQueue.currentSong.title, serverQueue.currentSong.displayUrl || serverQueue.currentSong.url))
                .setThumbnail(serverQueue.currentSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=🎵')
                .addFields({
                    name: `${EMOJIS.INFO} Resume`,
                    value: `${EMOJIS.PLAY} Use \`resume\` to continue playing`,
                    inline: false
                });

            message.reply({ embeds: [pauseEmbed] });
        } else {
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Pause Failed`)
                .setDescription(`${createStatusIndicator('error', 'Failed to pause the music')}\n\n${EMOJIS.INFO} Please try again.`);
            message.reply({ embeds: [errorEmbed] });
        }
    },
};
