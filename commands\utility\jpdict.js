const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle, ComponentType } = require('discord.js');
const axios = require('axios');
const wanakana = require('wanakana');

module.exports = {
  name: 'jisho',
  aliases: ['jpdict'],
  description: 'Search a Japanese word using Jisho.org!',
  category: 'utility',
  usePrefix: true,
  isEverywhere: false,

  async execute(message, args) {
    const query = args.join(' ');
    if (!query) return message.reply('❌ Please provide a word to search.');

    const url = `https://jisho.org/api/v1/search/words?keyword=${encodeURIComponent(query)}`;

    let res;
    try {
      res = await axios.get(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3'
        }
      });
    } catch (error) {
      return message.reply('❌ Failed to fetch data from Jisho.org.');
    }

    const data = res.data.data;
    if (!data.length) return message.reply('❌ No results found for that word.');

    let page = 0;
    const timeoutMs = 60000;
    const userId = message.author.id;

    const getEmbed = () => {
      const item = data[page];
      const japanese = item.japanese[0];
      const word = japanese.word || japanese.reading;
      const reading = japanese.reading;
      const romaji = wanakana.toRomaji(reading);

      const meanings = item.senses.map((sense, idx) => {
        const english = sense.english_definitions.join(', ');
        const partOfSpeech = sense.parts_of_speech.join(', ');
        return `**${idx + 1}.** ${english} _(${partOfSpeech || 'No part of speech'})_`;
      });

      const jlpt = item.jlpt.length ? item.jlpt.join(', ').toUpperCase() : 'N/A';
      const isCommon = item.is_common ? '✅ Yes' : '❌ No';
      const tags = item.senses[0]?.tags?.join(', ') || 'None';

      const embed = new EmbedBuilder()
        .setColor(0x0099ff)
        .setTitle(`${word} (${romaji})`)
        .setDescription(`**Reading:** ${reading}`)
        .addFields(
          { name: 'Meanings', value: meanings.slice(0, 3).join('\n') },
          { name: 'JLPT Level', value: jlpt, inline: true },
          { name: 'Common Word', value: isCommon, inline: true },
          { name: 'Usage Tags', value: tags, inline: false }
        )
        .setFooter({ text: `Result ${page + 1} of ${data.length}` });

      return embed;
    };

    const getRow = () => {
      return new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId('prev')
          .setLabel('⬅️ Prev')
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === 0),

        new ButtonBuilder()
          .setCustomId('next')
          .setLabel('Next ➡️')
          .setStyle(ButtonStyle.Secondary)
          .setDisabled(page === data.length - 1)
      );
    };

    const msg = await message.reply({
      embeds: [getEmbed()],
      components: [getRow()]
    });

    const collector = msg.createMessageComponentCollector({
      componentType: ComponentType.Button,
      time: timeoutMs,
      filter: i => i.user.id === userId
    });

    let timeout;

    const resetTimeout = () => {
      if (timeout) clearTimeout(timeout);
      timeout = setTimeout(() => collector.stop(), timeoutMs);
    };

    resetTimeout();

    collector.on('collect', async i => {
      await i.deferUpdate();

      if (i.customId === 'prev' && page > 0) page--;
      if (i.customId === 'next' && page < data.length - 1) page++;

      await msg.edit({
        embeds: [getEmbed()],
        components: [getRow()]
      });

      resetTimeout();
    });

    collector.on('end', () => {
      msg.edit({ components: [] }).catch(() => {});
    });
  }
};
