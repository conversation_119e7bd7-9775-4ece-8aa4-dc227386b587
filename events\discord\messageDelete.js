const { snipeDB } = require("../../database/manager")
module.exports = async (client, message) => {
    if (!message.guild || !message.author || message.author.bot) return;

    const data = {
        content: message.content || null,
        authorTag: message.author.tag,
        authorId: message.author.id,
        deletedAt: new Date().toISOString(),
        attachments: message.attachments?.map(att => att.url) || [],
        embeds: message.embeds?.map(e => ({
            title: e.title,
            description: e.description,
            url: e.url,
            image: e.image?.url,
            thumbnail: e.thumbnail?.url
        })) || [],
    };

    const key = `snipe_${message.guild.id}_${message.author.id}`;
    let logs = await snipeDB.get(key) || [];

    logs.unshift(data);
    logs = logs.slice(0, 10); // Limit to 10

    await snipeDB.set(key, logs);
};
