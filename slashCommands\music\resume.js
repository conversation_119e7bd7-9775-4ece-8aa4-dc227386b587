const { Slash<PERSON>ommandBuilder, <PERSON>bed<PERSON>uilder, MessageFlags } = require('discord.js');
const { useQueue } = require('discord-player');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('resume')
        .setDescription('Resume the current song'),
    
    async execute(interaction) {
        const queue = useQueue(interaction.guild.id);

        if (!queue || !queue.currentTrack) {
            return interaction.reply({
                embeds: [
                    new EmbedBuilder()
                        .setDescription('❌ There is no song playing!')
                        .setColor('#FF0000') // Red color
                ],
                flags:MessageFlags.Ephemeral
            });
        }

        queue.node.setPaused(false);
        return interaction.reply({
            embeds: [
                new EmbedBuilder()
                    .setDescription('▶️ Resumed the current song.')
                    .setColor('#00FF00') // Green color
            ]
        });
    }
};
