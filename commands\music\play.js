// Streaming-only Play Command - Fast, Simple, Efficient
const { PermissionsBitField } = require('discord.js');
const { Innertube } = require('youtubei.js');
const {
    joinVoiceChannel,
    createAudioPlayer,
    AudioPlayerStatus,
    VoiceConnectionStatus,
    entersState,
} = require('@discordjs/voice');
const { networkMonitor } = require('../../utils/networkStability');
const { 
    createThemedEmbed, 
    formatSongTitle, 
    createStatusIndicator,
    EMOJIS 
} = require('../../utils/embedTheme');
const {
    createStreamingResource,
    getSongInfoWithStreaming
} = require('../../utils/streamingUtils');

// Initialize YouTube service
let innertube;
(async () => {
    try {
        innertube = await Innertube.create();
    } catch (error) {
        console.error('Failed to initialize YouTube service:', error);
    }
})();

// Guild queues for streaming
const guildQueues = new Map();

/**
 * Stream next song in queue
 */
async function streamNext(guildId, textChannel) {
    const serverQueue = guildQueues.get(guildId);
    if (!serverQueue || serverQueue.songs.length === 0) {
        serverQueue.playing = false;
        return;
    }

    const songToStream = serverQueue.songs[0];
    serverQueue.currentSong = songToStream;

    try {
        // Get streaming URL if not already available (for playlist songs)
        if (!songToStream.streamingUrl) {
            const songInfo = await getSongInfoWithStreaming(songToStream.url);
            songToStream.streamingUrl = songInfo.streamingUrl;
            // Update metadata if needed
            if (songInfo.title && songInfo.title !== 'Unknown Title') {
                songToStream.title = songInfo.title;
            }
            if (songInfo.duration && songInfo.duration !== 'Unknown') {
                songToStream.duration = songInfo.duration;
            }
            if (songInfo.thumbnail) {
                songToStream.thumbnail = songInfo.thumbnail;
            }
        }

        // Create streaming resource
        const resource = await createStreamingResource(songToStream.streamingUrl, {
            volume: serverQueue.volume,
            metadata: { title: songToStream.title }
        });

        serverQueue.player.play(resource);
        serverQueue.playing = true;
        serverQueue.startTime = Date.now(); // Track when song started playing

        // Send now playing message for subsequent songs (not the first one)
        if (textChannel && serverQueue.songs.length > 1) {
            const nowPlayingEmbed = createThemedEmbed('playing')
                .setTitle(`${EMOJIS.PLAY} Now Playing`)
                .setDescription(formatSongTitle(songToStream.title, songToStream.url, true))
                .setThumbnail(songToStream.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=🎵')
                .addFields(
                    {
                        name: `${EMOJIS.TIMER} Duration`,
                        value: `${EMOJIS.CLOCK} ${songToStream.duration}`,
                        inline: true
                    },
                    {
                        name: `${EMOJIS.USER} Requested by`,
                        value: `${EMOJIS.HEART} ${songToStream.requestedBy}`,
                        inline: true
                    }
                );
            textChannel.send({ embeds: [nowPlayingEmbed] }).catch(console.error);
        }
    } catch (error) {
        console.error(`❌ Play failed for ${songToStream.title}:`, error.message);

        // Remove failed song and try next
        if (serverQueue.songs.length > 0) serverQueue.songs.shift();
        serverQueue.currentSong = null;
        serverQueue.startTime = null;

        if (textChannel) {
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Play Failed`)
                .setDescription(`${createStatusIndicator('error', `Failed to play: ${songToStream.title}`)}\n\n${EMOJIS.INFO} Trying next song...`);
            textChannel.send({ embeds: [errorEmbed] }).catch(console.error);
        }
        
        // Try next song
        if (serverQueue.songs.length > 0) {
            setTimeout(() => streamNext(guildId, textChannel), 1000);
        } else {
            serverQueue.playing = false;
        }
    }
}



/**
 * Handle single URL extraction
 */
async function handleSingleUrl(message, url) {
    try {
        const songInfo = await getSongInfoWithStreaming(url);
        songInfo.requestedBy = message.author.tag;
        songInfo.displayUrl = songInfo.url;
        return songInfo;
    } catch (error) {
        console.error(`❌ Failed to extract from URL: ${error.message}`);
        throw new Error(`Could not extract audio from this URL. The site may not be supported or the content may be unavailable.`);
    }
}

/**
 * Handle search query with enhanced YouTube.js search and fallbacks
 */
async function handleSearchQuery(message, query) {
    if (!innertube) {
        throw new Error('YouTube service not available');
    }

    try {
        // Enhanced search with multiple strategies
        let searchResults;
        let lastError;

        const searchStrategies = [
            { name: 'default', options: { type: 'video' } },
            { name: 'with_filter', options: { type: 'video', filter: 'video' } },
            { name: 'simple', options: {} }
        ];

        for (const strategy of searchStrategies) {
            try {
                searchResults = await innertube.search(query, strategy.options);

                if (searchResults && searchResults.videos && searchResults.videos.length > 0) {
                    break;
                }
            } catch (error) {
                lastError = error;
                console.warn(`❌ Search strategy ${strategy.name} failed: ${error.message}`);
                continue;
            }
        }

        if (!searchResults || !searchResults.videos || searchResults.videos.length === 0) {
            throw lastError || new Error(`No results found for "${query}"`);
        }

        // Find the best result (prefer non-live, non-short videos)
        const bestResult = findBestSearchResult(searchResults.videos);
        const youtubeUrl = `https://www.youtube.com/watch?v=${bestResult.id}`;

        const songInfo = await getSongInfoWithStreaming(youtubeUrl);

        // Enhance with search result data for better display
        enhanceSongInfoWithSearchData(songInfo, bestResult);
        songInfo.requestedBy = message.author.tag;
        songInfo.displayUrl = songInfo.url;

        return songInfo;

    } catch (error) {
        console.error(`❌ Search failed for "${query}": ${error.message}`);
        throw new Error(`Could not find any results for "${query}". Try different keywords or check your spelling.`);
    }
}

/**
 * Find the best search result from YouTube search results
 * @param {Array} videos - Array of video results
 * @returns {object} Best video result
 */
function findBestSearchResult(videos) {
    // Filter out unwanted content
    const filteredVideos = videos.filter(video => {
        // Skip if no ID
        if (!video.id) return false;

        // Skip live streams (prefer recorded content)
        if (video.is_live) return false;

        // Skip very short videos (likely shorts or clips)
        if (video.duration && video.duration.seconds_total && video.duration.seconds_total < 30) return false;

        // Skip very long videos (likely podcasts or streams)
        if (video.duration && video.duration.seconds_total && video.duration.seconds_total > 3600) return false;

        return true;
    });

    // Use filtered results if available, otherwise fall back to original
    const candidateVideos = filteredVideos.length > 0 ? filteredVideos : videos;

    // Sort by relevance factors
    const sortedVideos = candidateVideos.sort((a, b) => {
        // Prefer videos with higher view counts (more popular)
        const aViews = parseInt(a.view_count?.text?.replace(/[^\d]/g, '') || '0');
        const bViews = parseInt(b.view_count?.text?.replace(/[^\d]/g, '') || '0');

        // Prefer videos with reasonable duration (2-10 minutes)
        const aDuration = a.duration?.seconds_total || 0;
        const bDuration = b.duration?.seconds_total || 0;

        const aIsGoodDuration = aDuration >= 60 && aDuration <= 600;
        const bIsGoodDuration = bDuration >= 60 && bDuration <= 600;

        if (aIsGoodDuration && !bIsGoodDuration) return -1;
        if (!aIsGoodDuration && bIsGoodDuration) return 1;

        // Then by view count
        return bViews - aViews;
    });

    return sortedVideos[0] || videos[0];
}

/**
 * Enhance song info with search result data
 * @param {object} songInfo - Song info object to enhance
 * @param {object} searchResult - YouTube search result
 */
function enhanceSongInfoWithSearchData(songInfo, searchResult) {
    // Extract title with fallbacks
    if (searchResult.title) {
        const title = typeof searchResult.title === 'string' ? searchResult.title :
                     searchResult.title.text || searchResult.title.simpleText;
        if (title && title !== 'Unknown Title') {
            songInfo.title = title;
        }
    }

    // Extract thumbnail with quality preference
    if (searchResult.thumbnails && Array.isArray(searchResult.thumbnails) && searchResult.thumbnails.length > 0) {
        // Sort by quality and get the best one
        const sortedThumbnails = searchResult.thumbnails
            .filter(thumb => thumb && thumb.url)
            .sort((a, b) => {
                const aQuality = (a.width || 0) * (a.height || 0);
                const bQuality = (b.width || 0) * (b.height || 0);
                return bQuality - aQuality;
            });

        if (sortedThumbnails.length > 0) {
            songInfo.thumbnail = sortedThumbnails[0].url;
        }
    }

    // Extract duration with fallbacks
    if (searchResult.duration) {
        if (searchResult.duration.text) {
            songInfo.duration = searchResult.duration.text;
        } else if (searchResult.duration.seconds_total) {
            const totalSeconds = searchResult.duration.seconds_total;
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = Math.floor(totalSeconds % 60);

            if (hours > 0) {
                songInfo.duration = `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                songInfo.duration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
            }
        }
    }

    // Extract uploader/channel info
    if (searchResult.author) {
        const author = typeof searchResult.author === 'string' ? searchResult.author :
                      searchResult.author.name || searchResult.author.text || searchResult.author.simpleText;
        if (author && author !== 'Unknown') {
            songInfo.uploader = author;
        }
    }
}





/**
 * Check if URL is a YouTube playlist URL
 */
function isPlaylistUrl(url) {
    if (url.includes('youtube.com') || url.includes('youtu.be')) {
        // Check if it has a playlist parameter
        if (url.includes('list=')) {
            // Extract the list parameter value
            const listMatch = url.match(/[?&]list=([^&]+)/);
            if (listMatch && listMatch[1]) {
                const listId = listMatch[1];
                // Valid playlist IDs typically start with PL, UU, LL, or other specific prefixes
                // and are longer than 10 characters
                return listId.length > 10 && !listId.startsWith('WL'); // WL is "Watch Later" which is personal
            }
        }
    }
    return false;
}

/**
 * Extract playlist ID from YouTube URL
 */
function extractPlaylistId(url) {
    const regex = /[?&]list=([a-zA-Z0-9_-]+)/;
    const match = url.match(regex);
    return match ? match[1] : null;
}

/**
 * Handle playlist URL extraction and processing using youtubei.js
 */
async function handlePlaylistUrl(message, playlistUrl) {
    if (!innertube) {
        throw new Error('YouTube service not available');
    }

    try {
        // Send initial processing message
        const processingEmbed = createThemedEmbed('info')
            .setTitle(`${EMOJIS.LOADING} Processing Playlist`)
            .setDescription(`${createStatusIndicator('loading', 'Extracting playlist information...')}\n\n${EMOJIS.INFO} This may take a moment.`);

        const processingMessage = await message.reply({ embeds: [processingEmbed] });

        // Extract playlist ID
        const playlistId = extractPlaylistId(playlistUrl);
        if (!playlistId) {
            throw new Error('Invalid playlist URL');
        }

        // Get playlist info using youtubei.js (fast)
        const playlist = await innertube.getPlaylist(playlistId);
        if (!playlist || !playlist.videos) {
            throw new Error('Playlist not found or is private');
        }

        const playlistTitle = playlist.info?.title || 'Unknown Playlist';
        const playlistAuthor = playlist.info?.author?.name || 'Unknown';
        const videoCount = Math.min(playlist.videos.length, 50); // Limit to 50 videos

        // Update message with playlist info
        const playlistEmbed = createThemedEmbed('info')
            .setTitle(`${EMOJIS.PLAYLIST} Playlist Found`)
            .setDescription(`**${playlistTitle}**\n${EMOJIS.USER} ${playlistAuthor}`)
            .addFields(
                {
                    name: `${EMOJIS.MUSIC_NOTE} Videos Found`,
                    value: `${EMOJIS.SPARKLES} ${videoCount} videos`,
                    inline: true
                },
                {
                    name: `${EMOJIS.TIMER} Status`,
                    value: `${createStatusIndicator('loading', 'Adding to queue...')}`,
                    inline: true
                }
            );

        await processingMessage.edit({ embeds: [playlistEmbed] });

        // Process videos and add to queue efficiently
        const results = await handlePlaylistSongAdd(message, playlist, playlistTitle);

        // Update with final results
        const finalEmbed = createThemedEmbed('success')
            .setTitle(`${EMOJIS.SUCCESS} Playlist Added to Queue`)
            .setDescription(`**${playlistTitle}**\n${EMOJIS.USER} ${playlistAuthor}`)
            .addFields(
                {
                    name: `${EMOJIS.SUCCESS} Successfully Added`,
                    value: `${EMOJIS.SPARKLES} ${results.successful} songs`,
                    inline: true
                },
                {
                    name: `${EMOJIS.USER} Requested by`,
                    value: `${EMOJIS.HEART} ${message.author.tag}`,
                    inline: true
                }
            );

        if (results.failed > 0) {
            finalEmbed.addFields({
                name: `${EMOJIS.WARNING} Failed`,
                value: `${EMOJIS.ERROR} ${results.failed} songs (unavailable)`,
                inline: true
            });
        }

        await processingMessage.edit({ embeds: [finalEmbed] });

        return results;

    } catch (error) {
        console.error(`❌ Failed to process playlist: ${error.message}`);
        throw new Error(`Could not process playlist. ${error.message}`);
    }
}

/**
 * Handle playlist song addition to queue (efficient approach)
 */
async function handlePlaylistSongAdd(message, playlist, playlistTitle) {
    // Get or create server queue
    let serverQueue = await getOrCreateQueue(message);

    const wasEmpty = serverQueue.songs.length === 0;
    let successful = 0;
    let failed = 0;

    // Limit to 50 videos to prevent spam
    const videos = playlist.videos.slice(0, 50);

    // Add songs to queue with minimal metadata (streaming URLs will be fetched when needed)
    for (const video of videos) {
        try {
            if (!video.id) {
                failed++;
                continue;
            }

            // Create song info with basic metadata from youtubei.js
            const songInfo = {
                id: video.id,
                title: video.title?.text || video.title?.simpleText || 'Unknown Title',
                url: `https://www.youtube.com/watch?v=${video.id}`,
                streamingUrl: null, // Will be fetched when needed
                duration: video.duration?.text || 'Unknown',
                thumbnail: video.thumbnails?.[0]?.url || null,
                uploader: video.author?.name || 'Unknown',
                source: 'YouTube',
                isStream: true,
                canStream: true,
                requestedBy: message.author.tag,
                displayUrl: `https://www.youtube.com/watch?v=${video.id}`,
                fromPlaylist: true,
                playlistTitle: playlistTitle,
                extractionMethod: 'youtubei_playlist'
            };

            // Add to queue
            serverQueue.songs.push(songInfo);
            serverQueue.originalQueue.push({...songInfo});
            successful++;

        } catch (error) {
            console.warn(`❌ Failed to process video ${video.id}: ${error.message}`);
            failed++;
            continue;
        }
    }

    // Start playing if queue was empty and we have songs
    if (wasEmpty && successful > 0) {
        streamNext(message.guild.id, message.channel);
    }

    return { successful, failed, total: videos.length };
}

/**
 * Get related tracks for autoplay functionality
 */
async function getRelatedTracks(videoId, limit = 5) {
    if (!innertube) {
        return [];
    }

    try {
        const info = await innertube.getInfo(videoId);
        if (!info || !info.watch_next_feed) {
            return [];
        }

        // Get related videos from watch next feed
        const relatedVideos = info.watch_next_feed.filter(item =>
            item.type === 'CompactVideo' && item.id && item.title
        ).slice(0, limit);

        return relatedVideos.map(video => ({
            id: video.id,
            title: video.title?.text || 'Unknown Title',
            duration: video.duration?.text || 'Unknown',
            thumbnail: video.thumbnails?.[0]?.url || null,
            url: `https://www.youtube.com/watch?v=${video.id}`,
            uploader: video.author?.name || 'Unknown Artist',
            source: 'YouTube',
            isStream: true,
            canStream: true,
            extractionMethod: 'youtubei_autoplay'
        }));
    } catch (error) {
        console.warn('❌ Failed to get related tracks:', error.message);
        return [];
    }
}

/**
 * Add autoplay tracks to queue when it's empty
 */
async function handleAutoplay(guildId, lastTrack) {
    const serverQueue = guildQueues.get(guildId);
    if (!serverQueue || !serverQueue.autoplay || !lastTrack) {
        return;
    }

    try {
        // Extract video ID from the last track
        const videoId = lastTrack.id || extractVideoIdFromUrl(lastTrack.url);
        if (!videoId) {
            return;
        }

        // Get related tracks
        let relatedTracks = await getRelatedTracks(videoId, 5);

        // If no related tracks found, try search-based approach
        if (relatedTracks.length === 0 && lastTrack.uploader && lastTrack.uploader !== 'Unknown') {
            try {
                const searchQuery = `${lastTrack.uploader} music`;
                const searchResults = await innertube.search(searchQuery, { type: 'video' });

                if (searchResults && searchResults.videos && searchResults.videos.length > 0) {
                    relatedTracks = searchResults.videos.slice(0, 3).map(video => ({
                        id: video.id,
                        title: video.title?.text || 'Unknown Title',
                        duration: video.duration?.text || 'Unknown',
                        thumbnail: video.thumbnails?.[0]?.url || null,
                        url: `https://www.youtube.com/watch?v=${video.id}`,
                        uploader: video.author?.name || 'Unknown Artist',
                        source: 'YouTube',
                        isStream: true,
                        canStream: true,
                        extractionMethod: 'youtubei_search_autoplay'
                    }));
                }
            } catch (searchError) {
                console.warn('❌ Search-based autoplay failed:', searchError.message);
            }
        }

        if (relatedTracks.length === 0) {
            return;
        }

        // Filter out tracks that are already in history
        const historyUrls = new Set(serverQueue.playHistory?.map(t => t.url) || []);
        const filteredTracks = relatedTracks.filter(track =>
            !historyUrls.has(track.url) && track.url !== lastTrack.url
        );

        if (filteredTracks.length === 0) {
            return;
        }

        // Add autoplay tracks to queue
        const autoplayTracks = filteredTracks.slice(0, 2).map(track => ({
            ...track,
            streamingUrl: null, // Will be fetched when needed
            requestedBy: 'Autoplay',
            displayUrl: track.url,
            fromAutoplay: true,
            relatedTo: lastTrack.url
        }));

        serverQueue.songs.push(...autoplayTracks);

        // Send autoplay notification
        if (serverQueue.textChannel) {
            const autoplayEmbed = createThemedEmbed('info')
                .setTitle(`${EMOJIS.SPARKLES} Autoplay`)
                .setDescription(`${createStatusIndicator('info', 'Added related songs to keep the music going!')}\n\n${EMOJIS.MUSIC_NOTE} **${autoplayTracks.length} songs** added based on: **${lastTrack.title}**`)
                .addFields({
                    name: `${EMOJIS.INFO} Next Up`,
                    value: autoplayTracks.map((track, index) =>
                        `${index + 1}. ${formatSongTitle(track.title, track.url)}`
                    ).join('\n'),
                    inline: false
                });

            serverQueue.textChannel.send({ embeds: [autoplayEmbed] }).catch(console.error);
        }

        // Start playing the first autoplay track
        if (!serverQueue.playing) {
            streamNext(guildId, serverQueue.textChannel);
        }

    } catch (error) {
        console.warn('❌ Autoplay failed:', error.message);
    }
}

/**
 * Extract video ID from YouTube URL
 */
function extractVideoIdFromUrl(url) {
    const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([a-zA-Z0-9_-]+)/);
    return match ? match[1] : null;
}

/**
 * Handle single song addition to queue
 */
async function handleSingleSongAdd(message, songInfo) {
    // Get or create server queue
    let serverQueue = await getOrCreateQueue(message);

    // Add song to queue
    const wasEmpty = serverQueue.songs.length === 0;
    serverQueue.songs.push(songInfo);
    serverQueue.originalQueue.push({...songInfo});

    // Send added to queue message
    const addedEmbed = createThemedEmbed(wasEmpty ? 'playing' : 'success')
        .setTitle(`${wasEmpty ? EMOJIS.PLAY : EMOJIS.SUCCESS} ${wasEmpty ? 'Playing Now' : 'Added to Queue'}`)
        .setDescription(formatSongTitle(songInfo.title, songInfo.url))
        .setThumbnail(songInfo.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=🎵')
        .addFields(
            {
                name: `${EMOJIS.TIMER} Duration`,
                value: `${EMOJIS.CLOCK} ${songInfo.duration}`,
                inline: true
            },
            {
                name: `${EMOJIS.USER} Requested by`,
                value: `${EMOJIS.HEART} ${songInfo.requestedBy}`,
                inline: true
            }
        );

    await message.reply({ embeds: [addedEmbed] });

    // Start playing if queue was empty
    if (wasEmpty) {
        streamNext(message.guild.id, message.channel);
    }
}

/**
 * Get or create server queue
 */
async function getOrCreateQueue(message) {
    const voiceChannel = message.member?.voice?.channel;
    if (!voiceChannel) {
        throw new Error('You need to be in a voice channel!');
    }

    let serverQueue = guildQueues.get(message.guild.id);

    if (!serverQueue) {
        // Create streaming-optimized player with better timing
        const player = createAudioPlayer({
            behaviors: {
                noSubscriber: 'pause',
                maxMissedFrames: Math.round(5000 / 20),
            },
            debug: false
        });

        const queueConstruct = {
            textChannel: message.channel,
            voiceChannel: voiceChannel,
            connection: null,
            player: player,
            songs: [],
            playing: false,
            currentSong: null,
            loopMode: 'off',
            originalQueue: [],
            volume: 50,
            streamingMode: true,
            startTime: null, // Track when current song started playing
            autoplay: false, // Autoplay disabled by default
            playHistory: [], // Track played songs for autoplay filtering
        };

        guildQueues.set(message.guild.id, queueConstruct);
        serverQueue = queueConstruct;

        // Player event handlers
        player.on(AudioPlayerStatus.Idle, async () => {
            const q = guildQueues.get(message.guild.id);
            if (q) {
                q.playing = false;

                // Add current song to play history for autoplay filtering
                if (q.currentSong && !q.currentSong.fromAutoplay) {
                    q.playHistory = q.playHistory || [];
                    q.playHistory.push({
                        url: q.currentSong.url,
                        title: q.currentSong.title,
                        id: q.currentSong.id
                    });
                    // Keep only last 50 songs in history to prevent memory issues
                    if (q.playHistory.length > 50) {
                        q.playHistory = q.playHistory.slice(-50);
                    }
                }

                // Store reference to the song that just finished for autoplay
                const finishedSong = q.currentSong;

                // Handle loop modes
                if (q.loopMode === 'song' && q.currentSong) {
                    // Loop current song
                    streamNext(message.guild.id, q.textChannel);
                    return;
                }

                // Remove current song
                if (q.songs.length > 0) q.songs.shift();
                q.currentSong = null;
                q.startTime = null;

                // Handle queue loop
                if (q.loopMode === 'queue' && q.songs.length === 0 && q.originalQueue.length > 0) {
                    q.songs = [...q.originalQueue];
                }

                // Play next song or handle autoplay
                if (q.songs.length > 0) {
                    streamNext(message.guild.id, q.textChannel);
                } else if (q.autoplay && finishedSong && finishedSong.source === 'YouTube') {
                    // Try to add autoplay tracks when queue is empty
                    await handleAutoplay(message.guild.id, finishedSong);
                }
            }
        });

        player.on('error', (error) => {
            console.error('🎵 Player error:', error);
            const q = guildQueues.get(message.guild.id);
            if (q) {
                // Clean up current song and try to continue
                if (q.songs.length > 0) q.songs.shift();
                q.currentSong = null;
                q.playing = false;
                q.startTime = null;

                if (q.textChannel) {
                    const errorEmbed = createThemedEmbed('error')
                        .setTitle(`${EMOJIS.ERROR} Playback Error`)
                        .setDescription(`${createStatusIndicator('error', 'Playback interrupted')}\n\n${EMOJIS.INFO} Trying to continue...`);
                    q.textChannel.send({ embeds: [errorEmbed] }).catch(console.error);
                }

                // Try to play next song after a short delay
                if (q.songs.length > 0) {
                    setTimeout(() => streamNext(message.guild.id, q.textChannel), 1000);
                }
            }
        });

        // Join voice channel
        try {
            const connection = joinVoiceChannel({
                channelId: voiceChannel.id,
                guildId: message.guild.id,
                adapterCreator: message.guild.voiceAdapterCreator,
                selfDeaf: true,
                selfMute: false,
            });

            serverQueue.connection = connection;
            connection.subscribe(player);

            // Connection handlers
            connection.on(VoiceConnectionStatus.Disconnected, async () => {
                networkMonitor.recordDisconnection(message.guild.id, 'Streaming disconnected');
                try {
                    await Promise.race([
                        entersState(connection, VoiceConnectionStatus.Signalling, 5_000),
                        entersState(connection, VoiceConnectionStatus.Connecting, 5_000),
                    ]);
                    networkMonitor.recordReconnection(message.guild.id);
                } catch (err) {
                    networkMonitor.recordDisconnection(message.guild.id, 'Failed to reconnect');
                    if (connection.state.status !== VoiceConnectionStatus.Destroyed) {
                        connection.destroy();
                    }
                }
            });

            connection.on(VoiceConnectionStatus.Destroyed, () => {
                guildQueues.delete(message.guild.id);
            });

        } catch (error) {
            console.error('❌ Error joining voice channel:', error);
            guildQueues.delete(message.guild.id);
            throw error;
        }
    }

    return serverQueue;
}

module.exports = {
    name: 'play',
    description: 'Stream music instantly without downloads - fast and efficient!',
    aliases: ['p', 'stream', 'music'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    guildQueues: guildQueues, // Export for other commands
    async execute(message, args) {
        // Check voice channel
        const voiceChannel = message.member?.voice?.channel;
        if (!voiceChannel) {
            const noVoiceEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Voice Channel`)
                .setDescription(`${createStatusIndicator('error', 'You need to be in a voice channel!')}\n\n${EMOJIS.INFO} Join a voice channel and try again.`);
            return message.reply({ embeds: [noVoiceEmbed] });
        }

        // Check permissions
        const permissions = voiceChannel.permissionsFor(message.client.user);
        if (!permissions?.has(PermissionsBitField.Flags.Connect) || !permissions?.has(PermissionsBitField.Flags.Speak)) {
            const noPermEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Missing Permissions`)
                .setDescription(`${createStatusIndicator('error', 'I need permissions to connect and speak!')}`);
            return message.reply({ embeds: [noPermEmbed] });
        }

        // Show help if no arguments
        if (args.length === 0) {
            const helpEmbed = createThemedEmbed('info')
                .setTitle(`${EMOJIS.PLAY} Play Command`)
                .setDescription(`${createStatusIndicator('info', 'Play music instantly!')}\n\n**Features:**\n${EMOJIS.LIGHTNING} Fast enough i think\n${EMOJIS.SPARKLES} Stream directly from source\n${EMOJIS.FIRE} Zero storage usage\n${EMOJIS.HEART} Smart autoplay`)
                .addFields(
                    {
                        name: `${EMOJIS.INFO} Usage`,
                        value: `${EMOJIS.PLAY} \`play <song name>\` - Search and play\n${EMOJIS.MUSIC_NOTE} \`play <any URL>\` - Play from supported sites\n${EMOJIS.PLAYLIST} \`play <playlist URL>\` - Add entire playlists`,
                        inline: false
                    },
                    {
                        name: `${EMOJIS.SPARKLES} Examples`,
                        value: `${EMOJIS.SEARCH} \`play never gonna give you up\`\n${EMOJIS.LINK} \`play https://youtube.com/watch?v=...\`\n${EMOJIS.PLAYLIST} \`play https://youtube.com/playlist?list=...\`\n${EMOJIS.LINK} \`play https://x.com/i/status/...\``,
                        inline: false
                    },
                    {
                        name: `${EMOJIS.INFO} Supported Sites`,
                        value: `${EMOJIS.SPARKLES} YouTube, Twitter/X, SoundCloud, and 1000+ other sites!`,
                        inline: false
                    },
                    {
                        name: `${EMOJIS.HEART} Autoplay`,
                        value: `${EMOJIS.LIGHTNING} Use \`!autoplay\` to enable smart recommendations\n${EMOJIS.MUSIC_NOTE} Keeps music going when queue ends`,
                        inline: false
                    }
                );
            return message.reply({ embeds: [helpEmbed] });
        }

        const query = args.join(' ');

        try {
            // Check if it's a URL or search query using more robust URL detection
            const isUrl = /^https?:\/\//.test(query) || query.includes('://');

            if (isUrl) {
                // Check if it's a playlist URL
                if (isPlaylistUrl(query)) {
                    // Handle playlist URL
                    await handlePlaylistUrl(message, query);
                    return;
                } else {
                    // Handle single URL
                    const songInfo = await handleSingleUrl(message, query);
                    await handleSingleSongAdd(message, songInfo);
                    return;
                }
            } else {
                // Handle search query
                const songInfo = await handleSearchQuery(message, query);
                await handleSingleSongAdd(message, songInfo);
                return;
            }

        } catch (error) {
            console.error('❌ Play command error:', error);

            // Provide more specific error messages
            let errorDescription = 'Could not play the requested song';
            let troubleshooting = `${EMOJIS.SPARKLES} Try a different song\n${EMOJIS.INFO} Check if the URL is valid\n${EMOJIS.LIGHTNING} Ensure stable internet connection`;

            if (error.message.includes('region-blocked')) {
                errorDescription = 'This content is not available in your region';
                troubleshooting = `${EMOJIS.WARNING} Try a different song\n${EMOJIS.INFO} Content may be region-restricted\n${EMOJIS.SPARKLES} Search for an alternative version`;
            } else if (error.message.includes('unavailable')) {
                errorDescription = 'This video is unavailable or has been removed';
                troubleshooting = `${EMOJIS.ERROR} Video may be deleted or private\n${EMOJIS.SPARKLES} Try searching for the song again\n${EMOJIS.MUSIC_NOTE} Look for alternative uploads`;
            } else if (error.message.includes('authentication')) {
                errorDescription = 'Authentication required for this content';
                troubleshooting = `${EMOJIS.WARNING} Content requires special access\n${EMOJIS.SPARKLES} Try a different song\n${EMOJIS.MUSIC_NOTE} Look for publicly available versions`;
            } else if (error.message.includes('timeout') || error.message.includes('network')) {
                errorDescription = 'Network connection issue';
                troubleshooting = `${EMOJIS.LIGHTNING} Check your internet connection\n${EMOJIS.PROCESSING} Try again in a moment\n${EMOJIS.SPARKLES} Consider a different song`;
            }

            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Play Failed`)
                .setDescription(`${createStatusIndicator('error', errorDescription)}\n\n**Details:** ${error.message}`)
                .addFields({
                    name: `${EMOJIS.INFO} Troubleshooting`,
                    value: troubleshooting,
                    inline: false
                });

            await message.reply({ embeds: [errorEmbed] });
        }
    },
};
