const { VoiceConnectionStatus } = require('@discordjs/voice');
const fs = require('fs');
const {
    createThemedEmbed,
    createStatusIndicator,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

function deleteFile(filePath, retries = 3, delay = 1000) {
    if (!filePath || !fs.existsSync(filePath)) return;

    const attemptDelete = (attempt) => {
        fs.unlink(filePath, (err) => {
            if (err) {
                if (err.code === 'EBUSY' && attempt < retries) {
                    // File is busy, retry after delay
                    console.log(`File ${filePath} is busy, retrying in ${delay}ms... (attempt ${attempt + 1}/${retries})`);
                    setTimeout(() => attemptDelete(attempt + 1), delay);
                } else if (err.code === 'ENOENT') {
                    // File doesn't exist anymore, that's fine
                    return;
                } else {
                    console.error(`Error deleting file ${filePath} after ${attempt} attempts:`, err.message);
                }
            }
        });
    };

    attemptDelete(1);
}

module.exports = {
    name: 'stop',
    description: 'Stop the music and clear the queue',
    aliases: ['st', 'halt'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || !serverQueue.playing) {
            const noMusicEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Music Playing`)
                .setDescription(`${createStatusIndicator('error', 'There is no music currently playing!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to start playing music.`);
            return message.reply({ embeds: [noMusicEmbed] });
        }

        // Stop the player
        if (serverQueue.player) {
            serverQueue.player.stop(true);
        }

        // Clear the queue and clean up files
        if (serverQueue.songs) {
            serverQueue.songs.forEach(song => {
                if (song.filePath) {
                    deleteFile(song.filePath);
                }
            });
            serverQueue.songs = [];
        }

        // Clean up current song
        if (serverQueue.currentSong && serverQueue.currentSong.filePath) {
            deleteFile(serverQueue.currentSong.filePath);
        }

        // Reset queue state
        serverQueue.playing = false;
        serverQueue.currentSong = null;

        // Disconnect from voice channel
        if (serverQueue.connection && serverQueue.connection.state.status !== VoiceConnectionStatus.Destroyed) {
            serverQueue.connection.destroy();
        }

        // Remove the queue from the map
        guildQueues.delete(message.guild.id);

        const stopEmbed = createThemedEmbed('stopped')
            .setTitle(`${EMOJIS.STOP} Music Stopped`)
            .setDescription(`${createStatusIndicator('success', 'Music has been stopped and queue cleared')}\n\n${EMOJIS.INFO} Use \`play <song>\` to start playing music again.`);

        message.reply({ embeds: [stopEmbed] });
    },
};
