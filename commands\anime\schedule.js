const axios = require("axios");
const { Em<PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");

module.exports = {
    name: "schedule",
    aliases: ["airing"],
    category: "anime",
    description: "Get today's anime airing schedule with pagination.",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const validDays = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
        const today = new Date().toLocaleString("en-US", { weekday: "long" }).toLowerCase();
        
        let day = args[0] ? args[0].toLowerCase() : today;
        if (!validDays.includes(day)) {
            return message.reply(`❌ Invalid day. Please use: ${validDays.join(", ")}`);
        }

        let page = 1;
        const fetchSchedule = async (page) => {
            try {
                const response = await axios.get(`https://api.jikan.moe/v4/schedules?filter=${day}&page=${page}`);
                return response.data;
            } catch (error) {
                console.error("Error fetching schedule:", error);
                return null;
            }
        };

        const sendScheduleEmbed = async (page) => {
            const data = await fetchSchedule(page);
            if (!data || data.data.length === 0) {
                return message.reply(`❌ No anime is airing on **${day.charAt(0).toUpperCase() + day.slice(1)}**.`);
            }

            const animeList = data.data;
            const embed = new EmbedBuilder()
                .setColor("#ff9900")
                .setTitle(`📅 Anime Airing on ${day.charAt(0).toUpperCase() + day.slice(1)}`)
                .setDescription(
                    animeList.map((anime, i) => 
                        `**${(page - 1) * 25 + i + 1}. [${anime.title}](<${anime.url}>)**\n` +
                        `📌 **Episodes**: ${anime.episodes || "Unknown"}\n` +
                        `🎭 **Genres**: ${anime.genres.map(g => `[${g.name}](${g.url})`).join(", ") || "Unknown"}\n` +
                        `🕒 **Airing Time**: ${anime.broadcast?.string || "Unknown"}\n` +
                        `⭐ **Rating**: ${anime.rating || "Unknown"}\n` +
                        `🎥 **Trailer**: ${anime.trailer?.url ? `[Watch](<${anime.trailer.url}>)` : "Not available"}`
                    ).join("\n\n")
                )
                .setFooter({ text: `Page ${page} of ${data.pagination.last_visible_page}` });

            const buttons = new ActionRowBuilder().addComponents(
                new ButtonBuilder()
                    .setCustomId("prev_page")
                    .setLabel("⬅️ Previous")
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(page === 1),
                new ButtonBuilder()
                    .setCustomId("next_page")
                    .setLabel("➡️ Next")
                    .setStyle(ButtonStyle.Primary)
                    .setDisabled(!data.pagination.has_next_page)
            );

            const sentMessage = await message.channel.send({ embeds: [embed], components: [buttons] });

            const collector = sentMessage.createMessageComponentCollector({ time: 60000 });

            collector.on("collect", async (interaction) => {
                if (interaction.user.id !== message.author.id) {
                    return interaction.reply({ content: "❌ You can't control this pagination!", ephemeral: true });
                }

                if (interaction.customId === "prev_page" && page > 1) {
                    page--;
                } else if (interaction.customId === "next_page" && data.pagination.has_next_page) {
                    page++;
                }

                await interaction.deferUpdate();
                sendScheduleEmbed(page);
                sentMessage.delete();
            });

            collector.on("end", () => {
                sentMessage.edit({ components: [] }).catch(() => {});
            });
        };

        sendScheduleEmbed(page);
    }
};
