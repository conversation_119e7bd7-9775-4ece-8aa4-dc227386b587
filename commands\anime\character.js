const axios = require("axios");
const { <PERSON>bed<PERSON><PERSON>er, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");

module.exports = {
    name: "character",
    aliases: ["char"],
    category: "anime",
    description: "Cari informasi karakter anime dari MyAnimeList.",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const query = args.join(" ");
        if (!query) {
            return message.reply("❌ Masukkan nama karakter yang ingin dicari. Contoh: `!character Luffy`");
        }

        try {
            const searchRes = await axios.get(`https://api.jikan.moe/v4/characters?q=${encodeURIComponent(query)}&limit=10`);
            const characterList = searchRes.data.data;

            if (!characterList || characterList.length === 0) {
                return message.reply(`❌ Karakter dengan nama **"${query}"** tidak ditemukan.`);
            }

            let currentIndex = 0;
            let currentPage = 0;

            const truncate = (text, len) => text.length > len ? text.slice(0, len) + "..." : text;

            const fetchDetails = async (id) => {
                const res = await axios.get(`https://api.jikan.moe/v4/characters/${id}/full`);
                return res.data.data;
            };

            const splitChunks = (array, size = 5) => {
                const result = [];
                for (let i = 0; i < array.length; i += size) {
                    result.push(array.slice(i, i + size));
                }
                return result;
            };

            const createEmbed = async (index, page) => {
                const char = characterList[index];
                const details = await fetchDetails(char.mal_id);

                const embed = new EmbedBuilder()
                    .setColor("#ffcc00")
                    .setTitle(details.name)
                    .setURL(details.url)
                    .setDescription(truncate(details.about || "No biography available", 1000))
                    .setThumbnail(details.images.jpg?.image_url || null)
                    .addFields(
                        { name: "📌 Japanese", value: details.name_kanji || "None", inline: true },
                        { name: "📌 Nicknames", value: details.nicknames.length > 0 ? details.nicknames.join(", ") : "None", inline: true },
                        { name: "❤️ Favorites", value: details.favorites.toString(), inline: true }
                    )
                    .setFooter({ text: `Karakter ${index + 1}/${characterList.length} • Page ${page + 1}` });

                // Section Pagination: Voice Actors, Anime, Manga
                const voicePages = splitChunks(details.voices, 5);
                const animePages = splitChunks(details.anime, 5);
                const mangaPages = splitChunks(details.manga, 5);

                const voiceSection = voicePages[page] || [];
                const animeSection = animePages[page] || [];
                const mangaSection = mangaPages[page] || [];

                if (animeSection.length > 0) {
                    embed.addFields({
                        name: "🎬 Anime",
                        value: animeSection.map(a => `${a.anime.title} (${a.role})`).join("\n"),
                        inline: false
                    });
                }

                if (mangaSection.length > 0) {
                    embed.addFields({
                        name: "📚 Manga",
                        value: mangaSection.map(m => `${m.manga.title} (${m.role})`).join("\n"),
                        inline: false
                    });
                }

                if (voiceSection.length > 0) {
                    embed.addFields({
                        name: "🎤 Voice Actors",
                        value: voiceSection.map(v => `${v.person.name} (${v.language})`).join("\n"),
                        inline: false
                    });
                }

                return embed;
            };

            const createButtons = (index, page, totalPages) => {
                return new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId("prev_character")
                        .setLabel("◀ Character")
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(index === 0),
                    new ButtonBuilder()
                        .setCustomId("next_character")
                        .setLabel("Character ▶")
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(index === characterList.length - 1),
                    new ButtonBuilder()
                        .setCustomId("prev_page")
                        .setLabel("◀ Page")
                        .setStyle(ButtonStyle.Primary)
                        .setDisabled(page === 0),
                    new ButtonBuilder()
                        .setCustomId("next_page")
                        .setLabel("Page ▶")
                        .setStyle(ButtonStyle.Primary)
                        .setDisabled(page === totalPages - 1),
                    new ButtonBuilder()
                        .setLabel("View on MAL")
                        .setStyle(ButtonStyle.Link)
                        .setURL(characterList[index].url)
                );
            };

            const getTotalPages = (charData) => {
                const maxLength = Math.max(
                    Math.ceil(charData.voices.length / 5),
                    Math.ceil(charData.anime.length / 5),
                    Math.ceil(charData.manga.length / 5),
                    1
                );
                return maxLength;
            };

            const firstDetails = await fetchDetails(characterList[currentIndex].mal_id);
            let totalPages = getTotalPages(firstDetails);
            let embed = await createEmbed(currentIndex, currentPage);
            let buttons = createButtons(currentIndex, currentPage, totalPages);

            const sent = await message.channel.send({ embeds: [embed], components: [buttons] });

            const collector = sent.createMessageComponentCollector({
                filter: i => i.user.id === message.author.id,
                time: 60000
            });

            collector.on("collect", async (i) => {
                await i.deferUpdate();

                if (i.customId === "prev_character" && currentIndex > 0) {
                    currentIndex--;
                    currentPage = 0;
                } else if (i.customId === "next_character" && currentIndex < characterList.length - 1) {
                    currentIndex++;
                    currentPage = 0;
                } else if (i.customId === "prev_page" && currentPage > 0) {
                    currentPage--;
                } else if (i.customId === "next_page") {
                    const charData = await fetchDetails(characterList[currentIndex].mal_id);
                    totalPages = getTotalPages(charData);
                    if (currentPage < totalPages - 1) currentPage++;
                }

                const newEmbed = await createEmbed(currentIndex, currentPage);
                const newButtons = createButtons(currentIndex, currentPage, totalPages);

                await sent.edit({
                    embeds: [newEmbed],
                    components: [newButtons]
                });
            });

            collector.on("end", () => {
                sent.edit({ components: [] }).catch(() => {});
            });

        } catch (err) {
            console.error("Character fetch error:", err);
            return message.reply("❌ Terjadi kesalahan saat mengambil data karakter.");
        }
    }
};
