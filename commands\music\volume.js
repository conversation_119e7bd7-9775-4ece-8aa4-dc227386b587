const {
    createThemedEmbed,
    createStatusIndicator,
    getVolumeEmoji,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

module.exports = {
    name: 'volume',
    description: 'Set or display the current volume (0-100)',
    aliases: ['vol', 'v'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || !serverQueue.playing) {
            const noMusicEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Music Playing`)
                .setDescription(`${createStatusIndicator('error', 'There is no music currently playing!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to start playing music.`);
            return message.reply({ embeds: [noMusicEmbed] });
        }

        // Initialize volume if it doesn't exist
        if (!serverQueue.hasOwnProperty('volume')) {
            serverQueue.volume = 50; // Default volume 50%
        }

        // If no arguments, show current volume
        if (args.length === 0) {
            const volumeEmoji = getVolumeEmoji(serverQueue.volume);
            const currentVolumeEmbed = createThemedEmbed('volume')
                .setTitle(`${volumeEmoji} Current Volume`)
                .setDescription(`${createStatusIndicator('info', `Volume is currently set to **${serverQueue.volume}%**`)}`)
                .addFields({
                    name: `${EMOJIS.INFO} Usage`,
                    value: `${EMOJIS.SPARKLES} \`volume <0-100>\` to change volume\n${EMOJIS.VOLUME_MUTE} \`volume 0\` to mute\n${EMOJIS.VOLUME_HIGH} \`volume 100\` for maximum`,
                    inline: false
                });

            if (serverQueue.currentSong) {
                currentVolumeEmbed.addFields({
                    name: `${EMOJIS.MUSIC_NOTE} Currently Playing`,
                    value: `${EMOJIS.PLAY} ${serverQueue.currentSong.title}`,
                    inline: false
                });
            }

            return message.reply({ embeds: [currentVolumeEmbed] });
        }

        const newVolume = parseInt(args[0]);

        // Validate volume input
        if (isNaN(newVolume) || newVolume < 0 || newVolume > 100) {
            const invalidVolumeEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Invalid Volume`)
                .setDescription(`${createStatusIndicator('error', 'Volume must be a number between **0** and **100**!')}`)
                .addFields({
                    name: `${EMOJIS.SPARKLES} Examples`,
                    value: `${EMOJIS.VOLUME_MID} \`volume 50\` - Set volume to 50%\n${EMOJIS.VOLUME_MUTE} \`volume 0\` - Mute\n${EMOJIS.VOLUME_HIGH} \`volume 100\` - Maximum volume`,
                    inline: false
                });
            return message.reply({ embeds: [invalidVolumeEmbed] });
        }

        const oldVolume = serverQueue.volume;
        serverQueue.volume = newVolume;

        // Set the volume on the audio resource if it exists
        if (serverQueue.player && serverQueue.player.state.resource) {
            serverQueue.player.state.resource.volume?.setVolume(newVolume / 100);
        }

        // Get appropriate volume emoji using the utility function
        const volumeEmoji = getVolumeEmoji(newVolume);

        // Determine embed type based on volume level
        let embedType = 'volume';
        if (newVolume === 0) {
            embedType = 'warning';
        } else if (newVolume <= 25) {
            embedType = 'info';
        }

        const volumeEmbed = createThemedEmbed(embedType)
            .setTitle(`${volumeEmoji} Volume Changed`)
            .setDescription(`${createStatusIndicator('success', `Volume changed from **${oldVolume}%** to **${newVolume}%**`)}`);

        if (serverQueue.currentSong) {
            volumeEmbed.addFields({
                name: `${EMOJIS.MUSIC_NOTE} Currently Playing`,
                value: `${EMOJIS.PLAY} [${serverQueue.currentSong.title}](${serverQueue.currentSong.displayUrl || serverQueue.currentSong.url})`,
                inline: false
            });
        }

        message.reply({ embeds: [volumeEmbed] });
    },
};
