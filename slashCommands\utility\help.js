const { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, StringSelectMenuBuilder, MessageFlags } = require('discord.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help')
        .setDescription('Shows a list of available commands'),

    async execute(interaction) {
        await interaction.deferReply({ flags: MessageFlags.Ephemeral });

        const commandCategories = {};

        // Organize commands by category
        client.slashCommands.forEach(command => {
            const category = command.category || 'Slash'; // Default to 'Other' if no category is set
            if (!commandCategories[category]) {
                commandCategories[category] = [];
            }
            commandCategories[category].push(command);
        });

        const categoryNames = Object.keys(commandCategories);
        if (categoryNames.length === 0) {
            return interaction.editReply({ content: 'No commands available.', flags: MessageFlags.Ephemeral });
        }

        const helpEmbed = new EmbedBuilder()
            .setTitle('📜 Help Menu')
            .setDescription('Select a category below to view commands.')
            .setColor(0x00AE86)
            .setFooter({ text: 'Use !help <command> for text based commands' });

        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('help_select')
            .setPlaceholder('Choose a category')
            .addOptions(
                categoryNames.map(category => ({
                    label: category,
                    value: category.toLowerCase(),
                    description: `View ${category} commands`
                }))
            );

        const row = new ActionRowBuilder().addComponents(selectMenu);
        const reply = await interaction.editReply({ embeds: [helpEmbed], components: [row] });

        const filter = i => i.user.id === interaction.user.id;
        const collector = reply.createMessageComponentCollector({ filter, time: 60000 });

        collector.on('collect', async i => {
            const selectedCategory = i.values[0];
            const commands = commandCategories[selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)];

            const commandDetails = commands.map(cmd => {
                const options = cmd.data.options.map(opt => {
                    const choices = opt.choices ? ` (Choices: ${opt.choices.map(c => c.name).join(', ')})` : '';
                    return `- **${opt.name}** : ${opt.description}${choices} [Required: ${opt.required ? '✅' : '❌'}]`;
                }).join('\n') || '*No options*';

                return `### **/${cmd.data.name}**\n📄 ${cmd.data.description}\n🔹 **Options:**\n${options}`;
            }).join('\n\n');

            const categoryEmbed = new EmbedBuilder()
                .setTitle(`📂 ${selectedCategory.charAt(0).toUpperCase() + selectedCategory.slice(1)} Commands`)
                .setColor(0x00AE86)
                .setDescription(commandDetails);

            await i.update({ embeds: [categoryEmbed], components: [row] });
        });

        collector.on('end', async () => {
            await interaction.editReply({ components: [] });
        });
    }
};
