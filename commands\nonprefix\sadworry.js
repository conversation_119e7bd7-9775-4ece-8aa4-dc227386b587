const { PermissionsBitField } = require('discord.js');

module.exports = {
    name: 'sadworry',
    aliases: [''],
    category: 'nonprefix',
    description: 'Send an image using a webhook with your nickname and avatar.',
    usePrefix: false,
    isEverywhere: false,
    async execute(message, args) {
        const imageURL = 'https://cdn.discordapp.com/attachments/752708484444848138/1355328617239937104/sadworry.png?ex=67e887a3&is=67e73623&hm=46883ec2df8142584540302bd99e4cbbcf693d4b79eda2f41b99e527a4a4a514&'; // URL gambar

        let targetMember = message.member;
        

        // Cek izin membuat webhook
        if (!message.channel.permissionsFor(message.client.user).has(PermissionsBitField.Flags.ManageWebhooks)) {
            return message.reply('I need the "Manage Webhooks" permission to send images.');
        }

        try {
            await message.delete();
            // Buat webhook khusus user
            const webhook = await message.channel.createWebhook({
                name: targetMember.displayName, // Gunakan server nickname
                avatar: targetMember.displayAvatarURL({ dynamic: true })
            });

            // Kirim pesan sebagai webhook
            await webhook.send({
                content: imageURL,
                username: targetMember.displayName, // Gunakan server nickname
                avatarURL: targetMember.displayAvatarURL({ dynamic: true })
            });

            // Hapus webhook setelah 5 detik untuk menghindari spam
            setTimeout(async () => {
                await webhook.delete();
            }, 1000);

        } catch (error) {
            console.error('Error creating or sending webhook:', error);
            message.reply('Failed to send the image.');
        }
    }
};
