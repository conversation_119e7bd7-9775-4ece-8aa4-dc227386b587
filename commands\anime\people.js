const axios = require("axios");
const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, ButtonBuilder, ButtonStyle } = require("discord.js");

module.exports = {
    name: "people",
    aliases: ["seiyuu", "staff"],
    category: "anime",
    description: "Search for a person (such as a voice actor or staff) from MyAnimeList.",
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const query = args.join(" ");
        if (!query) {
            return message.reply("❌ Please provide a name. Example: `!people <PERSON><PERSON>");
        }

        try {
            const searchRes = await axios.get(`https://api.jikan.moe/v4/people?q=${encodeURIComponent(query)}&limit=10`);
            const peopleList = searchRes.data.data;

            if (!peopleList || peopleList.length === 0) {
                return message.reply(`❌ No person found for **"${query}"**.`);
            }

            let currentIndex = 0;

            const truncate = (text, max = 2000) => text.length > max ? text.slice(0, max) + "..." : text;

            const fetchDetailsEmbed = async (index) => {
                const person = peopleList[index];
                const detailsRes = await axios.get(`https://api.jikan.moe/v4/people/${person.mal_id}/full`);
                const details = detailsRes.data.data;

                return new EmbedBuilder()
                    .setColor("#ffcc00")
                    .setTitle(details.name)
                    .setURL(details.url)
                    .setDescription(truncate(details.about || "No biography available."))
                    .setThumbnail(details.images.jpg.image_url)
                    .addFields(
                        { name: "📌 Japanese Name", value: details.given_name || "None", inline: true },
                        { name: "📌 Family Name", value: details.family_name || "None", inline: true },
                        { name: "📌 Nicknames", value: details.alternate_names.length > 0 ? details.alternate_names.join(", ") : "None", inline: false },
                        { name: "📌 Birthplace", value: details.birth_place || "Unknown", inline: false },
                        { name: "📌 Birthdate", value: details.birthday ? new Date(details.birthday).toLocaleDateString() : "Unknown", inline: true },
                        { name: "📌 Deathdate", value: details.deathday ? new Date(details.deathday).toLocaleDateString() : "Still Alive", inline: true },
                        { name: "📌 Website", value: details.website_url || "None", inline: false }
                    )
                    .setFooter({ text: `Person ${index + 1} of ${peopleList.length}` });
            };

            const createNavigationRow = (index) => {
                return new ActionRowBuilder().addComponents(
                    new ButtonBuilder()
                        .setCustomId("prev_person")
                        .setLabel("◀ Prev")
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(index === 0),
                    new ButtonBuilder()
                        .setCustomId("next_person")
                        .setLabel("Next ▶")
                        .setStyle(ButtonStyle.Secondary)
                        .setDisabled(index === peopleList.length - 1),
                    new ButtonBuilder()
                        .setLabel("View on MAL")
                        .setStyle(ButtonStyle.Link)
                        .setURL(peopleList[index].url)
                );
            };

            const embed = await fetchDetailsEmbed(currentIndex);
            const buttons = createNavigationRow(currentIndex);

            const sentMessage = await message.channel.send({
                embeds: [embed],
                components: [buttons]
            });

            const collector = sentMessage.createMessageComponentCollector({
                filter: i => i.user.id === message.author.id,
                time: 60000
            });

            collector.on("collect", async (interaction) => {
                await interaction.deferUpdate();

                if (interaction.customId === "prev_person" && currentIndex > 0) {
                    currentIndex--;
                } else if (interaction.customId === "next_person" && currentIndex < peopleList.length - 1) {
                    currentIndex++;
                }

                const newEmbed = await fetchDetailsEmbed(currentIndex);
                const newButtons = createNavigationRow(currentIndex);

                await sentMessage.edit({
                    embeds: [newEmbed],
                    components: [newButtons]
                });
            });

            collector.on("end", () => {
                sentMessage.edit({ components: [] }).catch(() => {});
            });

        } catch (error) {
            console.error("Error fetching people data:", error);
            return message.reply("❌ An error occurred while fetching data. Please try again later.");
        }
    }
};
