const { <PERSON><PERSON>ow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, EmbedBuilder, MessageFlags } = require('discord.js');

const HIRAGANA_DATA = require('../../utils/hiragana.json');

function shuffleArray(array) {
  return [...array].sort(() => Math.random() - 0.5);
}

module.exports = {
  name: 'hiraganaquiz',
  aliases: ['hq'],
  category: 'game',
  description: 'Start a multiple-choice Hiragana quiz.',
  usePrefix: true,
  isEverywhere: false,

  async execute(message, args) {
    const loadingMsg = await message.channel.send('Starting Hiragana Quest...');

    let health = 3;
    let score = 0;

    while (health > 0) {
      const correctData = HIRAGANA_DATA[Math.floor(Math.random() * HIRAGANA_DATA.length)];
      const correctRomaji = correctData.romaji;

      const distractors = shuffleArray(
        HIRAGANA_DATA.filter(h => h.romaji !== correctRomaji)
      ).slice(0, 3);

      const options = shuffleArray([correctRomaji, ...distractors.map(d => d.romaji)]);
      const letters = ['A', 'B', 'C', 'D'];
      const correctIndex = options.indexOf(correctRomaji);
      const correctLetter = letters[correctIndex];

      const quizEmbed = new EmbedBuilder()
        .setColor(0xFAA61A)
        .setTitle('Hiragana Quiz')
        .setDescription(`What is the correct Romaji for this Hiragana?\n\n# ${correctData.char}`)
        .addFields({ name: 'Choices', value: options.map((opt, i) => `**${letters[i]})** ${opt}`).join('\n') })
        .setFooter({ text: `Health: ${'💖'.repeat(health)} | Score: ${score} | 30s timer` });

      const row = new ActionRowBuilder().addComponents(
        ...letters.map(letter =>
          new ButtonBuilder()
            .setCustomId(letter)
            .setLabel(letter)
            .setStyle(ButtonStyle.Primary)
        )
      );

      const quizMsg = await loadingMsg.edit({ content: '', embeds: [quizEmbed], components: [row] });

      const collector = quizMsg.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: 30000
      });

      let answered = false;

      collector.on('collect', async interaction => {
        const chosen = interaction.customId;
        if (chosen === correctLetter) {
          score++;
          await interaction.update({
            content: `✅ Correct! ${correctLetter}) ${correctRomaji}`,
            embeds: [],
            components: []
          });
        } else {
          health--;
          if (health === 0) {
            await interaction.update({
              content: `💀 Game over! Correct was **${correctLetter}) ${correctRomaji}**.`,
              embeds: [],
              components: []
            });
          } else {
            await interaction.reply({ content: `❌ Wrong! Health: ${'💖'.repeat(health)}`, flags: MessageFlags.Ephemeral });
          }
        }
        answered = true;
        collector.stop();
      });

      await new Promise(resolve => {
        collector.on('end', async () => {
          if (!answered) {
            health--;
            await quizMsg.edit({
              content: health === 0
                ? `💀 Time's up! The correct answer was **${correctLetter}) ${correctRomaji}**.`
                : `⏰ Time's up! Health left: ${'💖'.repeat(health)}. Correct answer: **${correctLetter}) ${correctRomaji}**.`,
              embeds: [],
              components: []
            });
          }
          resolve();
        });
      });
    }

    await message.channel.send(`🎉 Game over! Your final score: **${score}**`);
  }
};
