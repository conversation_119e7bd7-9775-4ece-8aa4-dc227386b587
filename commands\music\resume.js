const { AudioPlayerStatus } = require('@discordjs/voice');

const {
    createThemedEmbed,
    formatSongTitle,
    createStatusIndicator,
    EMOJIS
} = require('../../utils/embedTheme');

// Import the shared guildQueues from play.js
const playCommand = require('./play.js');
const guildQueues = playCommand.guildQueues;

module.exports = {
    name: 'resume',
    description: 'Resume the paused music',
    aliases: ['r', 'unpause'],
    category: 'music',
    usePrefix: true,
    isEverywhere: false,
    async execute(message, args) {
        const serverQueue = guildQueues.get(message.guild.id);

        if (!serverQueue || !serverQueue.currentSong) {
            const noMusicEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} No Music Playing`)
                .setDescription(`${createStatusIndicator('error', 'There is no music currently playing!')}\n\n${EMOJIS.INFO} Use \`play <song>\` to start playing music.`);
            return message.reply({ embeds: [noMusicEmbed] });
        }

        // Check if not paused
        if (serverQueue.player.state.status !== AudioPlayerStatus.Paused) {
            const notPausedEmbed = createThemedEmbed('warning')
                .setTitle(`${EMOJIS.PLAY} Already Playing`)
                .setDescription(`${createStatusIndicator('warning', 'The music is not paused!')}\n\n${EMOJIS.INFO} It's already playing.`);
            return message.reply({ embeds: [notPausedEmbed] });
        }

        // Resume the player
        const success = serverQueue.player.unpause();

        if (success) {
            const resumeEmbed = createThemedEmbed('playing')
                .setTitle(`${EMOJIS.PLAY} Music Resumed`)
                .setDescription(formatSongTitle(serverQueue.currentSong.title, serverQueue.currentSong.displayUrl || serverQueue.currentSong.url))
                .setThumbnail(serverQueue.currentSong.thumbnail || 'https://placehold.co/120x90/2B2D31/FFFFFF?text=🎵')
                .addFields({
                    name: `${EMOJIS.INFO} Status`,
                    value: `${EMOJIS.PLAY} Now playing`,
                    inline: false
                });

            message.reply({ embeds: [resumeEmbed] });
        } else {
            const errorEmbed = createThemedEmbed('error')
                .setTitle(`${EMOJIS.ERROR} Resume Failed`)
                .setDescription(`${createStatusIndicator('error', 'Failed to resume the music')}\n\n${EMOJIS.INFO} Please try again.`);
            message.reply({ embeds: [errorEmbed] });
        }
    },
};
